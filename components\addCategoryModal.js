const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('./buttons');
const { panelService } = require('../services');

module.exports = {
  customId: 'add_category_modal',
  async execute(interaction, client) {
    // الحصول على حالة الإعداد الحالية
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على القيم المدخلة
    const categoryName = interaction.fields.getTextInputValue('category_name_input');
    const categoryDescription = interaction.fields.getTextInputValue('category_description_input');
    let categoryEmoji = interaction.fields.getTextInputValue('category_emoji_input');

    // استخدام رمز تعبيري افتراضي إذا لم يتم إدخال أي رمز
    if (!categoryEmoji || categoryEmoji.trim() === '') {
      categoryEmoji = '🎫'; // رمز تعبيري افتراضي
    }

    // الحصول على اللوحة المحددة
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    // إضافة الفئة الجديدة إلى اللوحة
    const categories = selectedPanel.categories || [];
    categories.push({
      name: categoryName,
      description: categoryDescription,
      emoji: categoryEmoji
    });

    // تحديث الفئات في قاعدة البيانات
    await panelService.updatePanel(state.selectedPanel, { categories });

    // الحصول على معلومات اللوحات من قاعدة البيانات
    const panels = await panelService.getPanelsByGuild(interaction.guild.id);

    // الحصول على اللوحة المحددة بعد التحديث
    const updatedPanel = await panelService.getPanelById(state.selectedPanel);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');
    const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.select_placeholder');
    const successMessage = await client.translate(interaction.guild.id, 'panel_editor.category_added');

    // إعادة عرض واجهة محرر اللوحات مع الفئة المضافة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '\u200B', value: unsavedChanges }
      );

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_to_edit')
          .setPlaceholder(`[${updatedPanel.panelId.substring(0, 8)}...] ${updatedPanel.name}`)
          .addOptions(panels.map(panel => ({
            label: panel.name,
            value: panel.panelId,
            description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
          })))
      );

    // الحصول على ترجمات الإجراءات
    const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.action_placeholder');
    const sendPanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.label');
    const sendPanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.description');
    const editNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.label');
    const editNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.description');
    const editDescriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.label');
    const editDescriptionDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.description');
    const editWelcomeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.label');
    const editWelcomeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.description');
    const editDisplayTypeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.label');
    const editDisplayTypeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.description');
    const addCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.label');
    const addCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.description');
    const editRolesLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.label');
    const editRolesDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.description');
    const editTranscriptLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.label');
    const editTranscriptDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.description');
    const editCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.label');
    const editCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.description');
    const deletePanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.label');
    const deletePanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.description');

    const row2 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_action')
          .setPlaceholder(actionPlaceholder)
          .addOptions([
            {
              label: sendPanelLabel,
              value: 'send_panel',
              description: sendPanelDesc,
              emoji: '0️⃣'
            },
            {
              label: editNameLabel,
              value: 'edit_panel_name',
              description: editNameDesc,
              emoji: '1️⃣'
            },
            {
              label: editDescriptionLabel,
              value: 'edit_panel_description',
              description: editDescriptionDesc,
              emoji: '2️⃣'
            },
            {
              label: editWelcomeLabel,
              value: 'edit_welcome_message',
              description: editWelcomeDesc,
              emoji: '3️⃣'
            },
            {
              label: editDisplayTypeLabel,
              value: 'edit_display_type',
              description: editDisplayTypeDesc,
              emoji: '4️⃣'
            },
            {
              label: addCategoryLabel,
              value: 'add_category',
              description: addCategoryDesc,
              emoji: '5️⃣'
            },
            {
              label: editRolesLabel,
              value: 'edit_support_roles',
              description: editRolesDesc,
              emoji: '6️⃣'
            },
            {
              label: editTranscriptLabel,
              value: 'edit_transcript_channel',
              description: editTranscriptDesc,
              emoji: '7️⃣'
            },
            {
              label: editCategoryLabel,
              value: 'edit_ticket_category',
              description: editCategoryDesc,
              emoji: '8️⃣'
            },
            {
              label: deletePanelLabel,
              value: 'delete_panel',
              description: deletePanelDesc,
              emoji: '9️⃣'
            }
          ])
      );

    // الحصول على ترجمات الأزرار
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    const row3 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    // تحديث واجهة محرر اللوحات
    await interaction.update({
      embeds: [embed],
      components: [row1, row2, row3]
    });

    // ثم أرسل رسالة منفصلة تؤكد الإضافة
    await interaction.followUp({
      content: successMessage,
      ephemeral: true
    });
  }
};
