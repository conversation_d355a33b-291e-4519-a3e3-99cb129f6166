const { ActionRowBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, StringSelectMenuBuilder, ButtonBuilder } = require('discord.js');

function rebuildComponents(rawComponents) {
  return rawComponents.map(row => {
    const newRow = new ActionRowBuilder();
    row.components.forEach(component => {
      if (component.type === 2) { // زر Button
        const button = new ButtonBuilder(component);
        button.setDisabled(false);
        newRow.addComponents(button);
      } else if (component.type === 3) { // قائمة SelectMenu
        // إعادة بناء خيارات SelectMenu مع التحقق من الحقول المطلوبة
        const options = component.options.map(opt => ({
          label: opt.label || 'بدون عنوان',
          value: opt.value || 'undefined',
          description: opt.description || undefined,
          emoji: opt.emoji || undefined,
          default: opt.default || false
        }));
        const selectMenu = new StringSelectMenuBuilder()
          .setCustomId(component.customId)
          .setPlaceholder(component.placeholder || 'اختر خياراً')
          .setMinValues(component.minValues || 1)
          .setMaxValues(component.maxValues || 1)
          .setDisabled(false)
          .addOptions(options);
        newRow.addComponents(selectMenu);
      }
    });
    return newRow;
  });
}

module.exports = {
  id: 'ticket_actions',

  async execute(interaction, client) {
    const selectedValue = interaction.values[0];
    const channel = interaction.channel;

    try {
      switch (selectedValue) {
        case 'add_member':
          await require('../../utils/ticketManager').addMemberToTicket(interaction, client);
          {
            // لا تعيد إرسال القائمة، فقط رد مختصر
            try {
              if (!interaction.replied && !interaction.deferred) {
                await interaction.deferUpdate();
              } else {
                await interaction.followUp({ content: 'تمت العملية بنجاح.', ephemeral: true });
              }
            } catch (error) {
              console.error('Error handling interaction:', error);
            }
          }
          break;

        case 'remove_member':
          await require('../../utils/ticketManager').removeMemberFromTicket(interaction, client);
          {
            try {
              if (!interaction.replied && !interaction.deferred) {
                await interaction.deferUpdate();
              } else {
                await interaction.followUp({ content: 'تمت العملية بنجاح.', ephemeral: true });
              }
            } catch (error) {
              console.error('Error handling interaction:', error);
            }
          }
          break;

        case 'close_ticket':
          await require('../../utils/ticketManager').closeTicket(interaction, client);
          {
            try {
              if (!interaction.replied && !interaction.deferred) {
                await interaction.deferUpdate();
              } else {
                await interaction.followUp({ content: 'تمت العملية بنجاح.', ephemeral: true });
              }
            } catch (error) {
              console.error('Error handling interaction:', error);
            }
          }
          break;

        case 'send_reminder':
          await require('../../utils/ticketManager').sendReminder(interaction, client);
          break;

        default:
          await interaction.reply({ content: 'خيار غير معروف.', ephemeral: true });
          break;
      }
    } catch (error) {
      console.error('Error handling ticket action:', error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({ content: 'حدث خطأ أثناء معالجة الإجراء.', ephemeral: true });
      }
    }
  }
};
