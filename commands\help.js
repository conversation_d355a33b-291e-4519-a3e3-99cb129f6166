const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('help')
    .setDescription('Display help information about the bot'),

  async execute(interaction, client) {
    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'help.title');
    const description = await client.translate(interaction.guild.id, 'help.description');

    // الحصول على ترجمات الأوامر
    const setupCommand = await client.translate(interaction.guild.id, 'help.commands.setup');
    const helpCommand = await client.translate(interaction.guild.id, 'help.commands.help');
    const debugCommand = await client.translate(interaction.guild.id, 'help.commands.debug');
    const setlangCommand = await client.translate(interaction.guild.id, 'help.commands.setlang');

    // الحصول على ترجمات الأزرار
    const supportButton = await client.translate(interaction.guild.id, 'help.support_button');

    // إنشاء الإطار
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '/setup', value: setupCommand },
        { name: '/help', value: helpCommand },
        { name: '/debug', value: debugCommand },
        { name: '/setlang', value: setlangCommand }
      )
      .setFooter({ text: client.config.footerText });

    // إنشاء الأزرار
    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setLabel(supportButton)
          .setURL(client.config.supportServer)
          .setStyle(ButtonStyle.Link)
      );

    await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
  }
};
