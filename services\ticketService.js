const { v4: uuidv4 } = require('uuid');
const Ticket = require('../models/Ticket');

// خدمة إدارة التذاكر
const ticketService = {
  // إنشاء تذكرة جديدة
  createTicket: async (ticketData) => {
    try {
      // إنشاء معرف فريد للتذكرة
      const ticketId = uuidv4();

      // إنشاء تذكرة جديدة
      const ticket = new Ticket({
        ticketId,
        guildId: ticketData.guildId,
        channelId: ticketData.channelId,
        userId: ticketData.userId,
        panelId: ticketData.panelId,
        category: ticketData.category,
        status: 'open',
        number: ticketData.number || 1
      });

      // حفظ التذكرة في قاعدة البيانات
      await ticket.save();
      return ticket;
    } catch (error) {
      console.error('Error creating ticket:', error);
      return null;
    }
  },

  // دالة جديدة: الحصول على تذكرة مفتوحة للمستخدم في سيرفر معين
  getOpenTicketByUserId: async (guildId, userId) => {
    try {
      return await Ticket.findOne({ guildId, userId, status: 'open' });
    } catch (error) {
      console.error('Error getting open ticket by user ID:', error);
      return null;
    }
  },

  // الحصول على تذكرة بواسطة معرف القناة
  getTicketByChannelId: async (channelId) => {
    try {
      return await Ticket.findOne({ channelId });
    } catch (error) {
      console.error('Error getting ticket by channel ID:', error);
      return null;
    }
  },

  // الحصول على تذكرة بواسطة المعرف
  getTicketById: async (ticketId) => {
    try {
      return await Ticket.findOne({ ticketId });
    } catch (error) {
      console.error('Error getting ticket by ID:', error);
      return null;
    }
  },

  // الحصول على جميع التذاكر للسيرفر
  getTicketsByGuild: async (guildId) => {
    try {
      return await Ticket.find({ guildId });
    } catch (error) {
      console.error('Error getting tickets by guild:', error);
      return [];
    }
  },

  // الحصول على جميع التذاكر للمستخدم
  getTicketsByUser: async (userId) => {
    try {
      return await Ticket.find({ userId });
    } catch (error) {
      console.error('Error getting tickets by user:', error);
      return [];
    }
  },

  // الحصول على آخر رقم تذكرة للسيرفر
  getLastTicketNumber: async (guildId) => {
    try {
      const lastTicket = await Ticket.findOne({ guildId }).sort({ number: -1 });
      return lastTicket ? lastTicket.number : 0;
    } catch (error) {
      console.error('Error getting last ticket number:', error);
      return 0;
    }
  },

  // تحديث تذكرة
  updateTicket: async (ticketId, updateData) => {
    try {
      const result = await Ticket.updateOne(
        { ticketId },
        { $set: { ...updateData, updatedAt: Date.now() } }
      );
      return result.modifiedCount > 0;
    } catch (error) {
      console.error('Error updating ticket:', error);
      return false;
    }
  },

  // إغلاق تذكرة
  closeTicket: async (ticketId, closedBy) => {
    try {
      const result = await Ticket.updateOne(
        { ticketId },
        {
          $set: {
            status: 'closed',
            closedBy,
            closedAt: Date.now(),
            updatedAt: Date.now()
          }
        }
      );
      return result.modifiedCount > 0;
    } catch (error) {
      console.error('Error closing ticket:', error);
      return false;
    }
  },

  // إعادة فتح تذكرة
  reopenTicket: async (ticketId) => {
    try {
      const result = await Ticket.updateOne(
        { ticketId },
        {
          $set: {
            status: 'open',
            updatedAt: Date.now()
          },
          $unset: {
            closedBy: 1,
            closedAt: 1
          }
        }
      );
      return result.modifiedCount > 0;
    } catch (error) {
      console.error('Error reopening ticket:', error);
      return false;
    }
  },

  // حذف تذكرة
  deleteTicket: async (ticketId) => {
    try {
      const result = await Ticket.deleteOne({ ticketId });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting ticket:', error);
      return false;
    }
  }
};

module.exports = ticketService;
