const { Embed<PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'back_to_panel_action',

  async execute(interaction, client) {
    try {
      // استخراج معرف اللوحة من معرف الزر
      const customIdParts = interaction.customId.split('_');
      const panelId = customIdParts[4];

      // الحصول على حالة الإعداد
      const state = setupStates.get(interaction.user.id);

      if (!state) {
        const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }

      // تحديث الحالة
      state.selectedPanel = panelId;
      setupStates.set(interaction.user.id, state);

      // الحصول على معلومات اللوحة من قاعدة البيانات
      const selectedPanel = await panelService.getPanelById(state.selectedPanel);

      if (!selectedPanel) {
        const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }

      // الحصول على الترجمات
      const title = await client.translate(interaction.guild.id, 'panel_editor.title');
      const description = await client.translate(interaction.guild.id, 'panel_editor.description');
      const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.action_placeholder');

      // الحصول على ترجمات الإجراءات
      const sendPanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.label');
      const sendPanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.description');
      const editNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.label');
      const editNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.description');
      const editDescriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.label');
      const editDescriptionDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.description');
      const editWelcomeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.label');
      const editWelcomeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.description');
      const editDisplayTypeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.label');
      const editDisplayTypeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.description');
      const addCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.label');
      const addCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.description');
      const editRolesLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.label');
      const editRolesDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.description');
      const editTranscriptLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.label');
      const editTranscriptDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.description');
      const editCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.label');
      const editCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.description');
      const editTicketNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.label');
      const editTicketNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.description');
      const deletePanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.label');
      const deletePanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.description');

      // إنشاء الإمبد
      const embed = new EmbedBuilder()
        .setColor(client.config.embedColor)
        .setTitle(`${title} - ${selectedPanel.name}`)
        .setDescription(description);

      // إنشاء القائمة المنسدلة
      const row = new ActionRowBuilder()
        .addComponents(
          new StringSelectMenuBuilder()
            .setCustomId('select_panel_action')
            .setPlaceholder(actionPlaceholder)
            .addOptions([
              {
                label: sendPanelLabel,
                value: 'send_panel',
                description: sendPanelDesc,
                emoji: '0️⃣'
              },
              {
                label: editNameLabel,
                value: 'edit_panel_name',
                description: editNameDesc,
                emoji: '1️⃣'
              },
              {
                label: editDescriptionLabel,
                value: 'edit_panel_description',
                description: editDescriptionDesc,
                emoji: '2️⃣'
              },
              {
                label: editWelcomeLabel,
                value: 'edit_welcome_message',
                description: editWelcomeDesc,
                emoji: '3️⃣'
              },
              {
                label: editDisplayTypeLabel,
                value: 'edit_display_type',
                description: editDisplayTypeDesc,
                emoji: '4️⃣'
              },
              {
                label: addCategoryLabel,
                value: 'add_category',
                description: addCategoryDesc,
                emoji: '5️⃣'
              },
              {
                label: editRolesLabel,
                value: 'edit_support_roles',
                description: editRolesDesc,
                emoji: '6️⃣'
              },
              {
                label: editTranscriptLabel,
                value: 'edit_transcript_channel',
                description: editTranscriptDesc,
                emoji: '7️⃣'
              },
              {
                label: editTicketNameLabel,
                value: 'edit_ticket_name',
                description: editTicketNameDesc,
                emoji: '8️⃣'
              },
              {
                label: editCategoryLabel,
                value: 'edit_ticket_category',
                description: editCategoryDesc,
                emoji: '9️⃣'
              },
              {
                label: deletePanelLabel,
                value: 'delete_panel',
                description: deletePanelDesc,
                emoji: '🔟'
              }
            ])
        );

      // تحديث الرسالة
      await interaction.update({
        embeds: [embed],
        components: [row]
      });
    } catch (error) {
      console.error('Error going back to panel action:', error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.button_error');
      await interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }
  }
};
