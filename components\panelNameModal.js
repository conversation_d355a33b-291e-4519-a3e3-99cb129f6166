const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { setupStates } = require('./buttons');

module.exports = {
  id: 'panel_name_modal',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على اسم اللوحة من النموذج
    const panelName = interaction.fields.getTextInputValue('panel_name_input');

    // تحديث اسم اللوحة في حالة الإعداد
    state.panelName = panelName;
    setupStates.set(interaction.user.id, state);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'setup.steps.step1.title');
    const description = await client.translate(interaction.guild.id, 'setup.steps.step1.description');
    const currentName = await client.translate(interaction.guild.id, 'setup.steps.step1.current_name');
    const setNameButton = await client.translate(interaction.guild.id, 'setup.steps.step1.set_name_button');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

    // إعادة عرض الخطوة الأولى مع الاسم المحدث
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    embed.addFields({ name: currentName, value: state.panelName });

    const row1 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('set_panel_name')
          .setLabel(setNameButton)
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🔄')
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('setup_continue')
          .setLabel(continueButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
