const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'edit_panels',

  async execute(interaction, client) {
    // إنشاء حالة إعداد جديدة للمستخدم إذا لم تكن موجودة
    if (!setupStates.has(interaction.user.id)) {
      setupStates.set(interaction.user.id, {
        step: 'edit_panels',
        selectedPanel: null,
        selectedAction: null
      });
    } else {
      // تحديث الحالة الحالية
      const state = setupStates.get(interaction.user.id);
      state.step = 'edit_panels';
      state.selectedPanel = null;
      state.selectedAction = null;
      setupStates.set(interaction.user.id, state);
    }

    // الحصول على قائمة اللوحات المتاحة من قاعدة البيانات
    const panels = await panelService.getPanelsByGuild(interaction.guild.id);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');
    const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.select_placeholder');
    const noPanelsLabel = await client.translate(interaction.guild.id, 'panel_editor.no_panels.label');
    const noPanelsDescription = await client.translate(interaction.guild.id, 'panel_editor.no_panels.description');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    // إنشاء خيارات القائمة المنسدلة
    const panelOptions = panels.length > 0
      ? panels.map(panel => ({
          label: panel.name,
          value: panel.panelId,
          description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
        }))
      : [
          {
            label: noPanelsLabel,
            value: 'no_panels',
            description: noPanelsDescription
          }
        ];

    // إنشاء واجهة محرر اللوحات
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '\u200B', value: unsavedChanges }
      );

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_to_edit')
          .setPlaceholder(selectPlaceholder)
          .addOptions(panelOptions)
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
