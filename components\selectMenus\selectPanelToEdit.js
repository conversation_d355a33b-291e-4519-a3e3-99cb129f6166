const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_panel_to_edit',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // تحديث اللوحة المحددة
    state.selectedPanel = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const panels = await panelService.getPanelsByGuild(interaction.guild.id);

    // الحصول على رسالة الخطأ
    const noPanelsError = await client.translate(interaction.guild.id, 'errors.no_panels');

    // التحقق من وجود لوحات
    if (panels.length === 0) {
      try {
        if (interaction.replied || interaction.deferred) {
          return interaction.followUp({
            content: noPanelsError,
            ephemeral: true
          });
        } else {
          return interaction.reply({
            content: noPanelsError,
            ephemeral: true
          });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
        return;
      }
    }

    // التحقق من قيمة no_panels
    if (state.selectedPanel === 'no_panels') {
      try {
        if (interaction.replied || interaction.deferred) {
          return interaction.followUp({
            content: noPanelsError,
            ephemeral: true
          });
        } else {
          return interaction.reply({
            content: noPanelsError,
            ephemeral: true
          });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
        return;
      }
    }

    // الحصول على اللوحة المحددة
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      try {
        if (interaction.replied || interaction.deferred) {
          return interaction.followUp({
            content: errorMessage,
            ephemeral: true
          });
        } else {
          return interaction.reply({
            content: errorMessage,
            ephemeral: true
          });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
        return;
      }
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');

    // إنشاء واجهة محرر اللوحات مع اللوحة المحددة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '\u200B', value: unsavedChanges }
      );

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_to_edit')
          .setPlaceholder(`[${selectedPanel.panelId.substring(0, 8)}...] ${selectedPanel.name}`)
          .addOptions(panels.map(panel => ({
            label: panel.name,
            value: panel.panelId,
            description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
          })))
      );

    // الحصول على ترجمات الإجراءات
    const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.action_placeholder');
    const sendPanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.label');
    const sendPanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.description');
    const editNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.label');
    const editNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.description');
    const editDescriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.label');
    const editDescriptionDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.description');
    const editWelcomeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.label');
    const editWelcomeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.description');
    const editDisplayTypeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.label');
    const editDisplayTypeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.description');
    const addCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.label');
    const addCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.description');
    const editRolesLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.label');
    const editRolesDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.description');
    const editTranscriptLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.label');
    const editTranscriptDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.description');
    const editCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.label');
    const editCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.description');
    const editTicketNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.label');
    const editTicketNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.description');
    const deletePanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.label');
    const deletePanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.description');

    const row2 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_action')
          .setPlaceholder(actionPlaceholder)
          .addOptions([
            {
              label: sendPanelLabel,
              value: 'send_panel',
              description: sendPanelDesc,
              emoji: '0️⃣'
            },
            {
              label: editNameLabel,
              value: 'edit_panel_name',
              description: editNameDesc,
              emoji: '1️⃣'
            },
            {
              label: editDescriptionLabel,
              value: 'edit_panel_description',
              description: editDescriptionDesc,
              emoji: '2️⃣'
            },
            {
              label: editWelcomeLabel,
              value: 'edit_welcome_message',
              description: editWelcomeDesc,
              emoji: '3️⃣'
            },
            {
              label: editDisplayTypeLabel,
              value: 'edit_display_type',
              description: editDisplayTypeDesc,
              emoji: '4️⃣'
            },
            {
              label: addCategoryLabel,
              value: 'add_category',
              description: addCategoryDesc,
              emoji: '5️⃣'
            },
            {
              label: editRolesLabel,
              value: 'edit_support_roles',
              description: editRolesDesc,
              emoji: '6️⃣'
            },
            {
              label: editTranscriptLabel,
              value: 'edit_transcript_channel',
              description: editTranscriptDesc,
              emoji: '7️⃣'
            },
            {
              label: editCategoryLabel,
              value: 'edit_ticket_category',
              description: editCategoryDesc,
              emoji: '8️⃣'
            },
            {
              label: editTicketNameLabel,
              value: 'edit_ticket_name',
              description: editTicketNameDesc,
              emoji: '9️⃣'
            },
            {
              label: deletePanelLabel,
              value: 'delete_panel',
              description: deletePanelDesc,
              emoji: '🔟'
            }
          ])
      );

    // الحصول على ترجمات الأزرار
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    const row3 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    try {
      // تأجيل الرد إذا لم يتم الرد بعد
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate().catch(e => console.error('Error deferring update:', e));
      }

      // محاولة تحديث التفاعل
      await interaction.editReply({ embeds: [embed], components: [row1, row2, row3] }).catch(async (error) => {
        console.error('Error updating interaction:', error);

        // محاولة إرسال رسالة متابعة إذا فشل التحديث
        await interaction.followUp({
          embeds: [embed],
          components: [row1, row2, row3],
          ephemeral: true
        }).catch(followUpError => {
          console.error('Failed to follow up on interaction:', followUpError);
        });
      });
    } catch (error) {
      console.error('Unexpected error in interaction handling:', error);
    }
  }
};
