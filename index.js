// استيراد المكتبات اللازمة
const { Client, GatewayIntentBits, Partials, Collection, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const https = require('https');
const { pipeline } = require('stream');
const { promisify } = require('util');
require('dotenv').config();

const streamPipeline = promisify(pipeline);

async function fetchImageBuffer(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      if (res.statusCode !== 200) {
        reject(new Error(`Failed to get image. Status code: ${res.statusCode}`));
        return;
      }
      const data = [];
      res.on('data', chunk => data.push(chunk));
      res.on('end', () => resolve(Buffer.concat(data)));
    }).on('error', reject);
  });
}

// استيراد خدمات قاعدة البيانات
const { connectDB } = require('./utils/database');

// استيراد مدير الترجمة
const { translate } = require('./utils/localeManager');

// استيراد الخدمات
const { guildService, panelService, ticketService } = require('./services');

// إنشاء عميل Discord مع الصلاحيات اللازمة
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.DirectMessages
  ],
  partials: [
    Partials.Channel,
    Partials.Message,
    Partials.User,
    Partials.GuildMember,
    Partials.Reaction
  ]
});

// استيراد الإعدادات
const config = require('./config.json');

// إنشاء مجموعات للأوامر والأزرار
client.commands = new Collection();
client.buttons = new Collection();
client.selectMenus = new Collection();
client.panels = new Collection();
client.config = config;
client.translate = translate; // إضافة وظيفة الترجمة إلى العميل

// تعيين الخدمات في كائن واحد للوصول الموحد
client.services = {
  guildService,
  panelService,
  ticketService
};

// تحميل الأوامر
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  const command = require(filePath);

  if ('data' in command && 'execute' in command) {
    client.commands.set(command.data.name, command);
  } else {
    console.log(`[تحذير] الأمر في ${filePath} يفتقد إلى خاصية "data" أو "execute" المطلوبة.`);
  }
}

// تحميل مكونات الواجهة
const componentsPath = path.join(__dirname, 'components');
if (fs.existsSync(componentsPath)) {
  // تحميل الأزرار
  const buttonsPath = path.join(componentsPath, 'buttons');
  if (fs.existsSync(buttonsPath)) {
    const buttonFiles = fs.readdirSync(buttonsPath).filter(file => file.endsWith('.js'));
    for (const file of buttonFiles) {
      const filePath = path.join(buttonsPath, file);
      const button = require(filePath);
      client.buttons.set(button.id, button);
    }
  }

  // تحميل القوائم المنسدلة
  const selectMenusPath = path.join(componentsPath, 'selectMenus');
  if (fs.existsSync(selectMenusPath)) {
    const selectMenuFiles = fs.readdirSync(selectMenusPath).filter(file => file.endsWith('.js'));
    for (const file of selectMenuFiles) {
      const filePath = path.join(selectMenusPath, file);
      const selectMenu = require(filePath);
      client.selectMenus.set(selectMenu.id, selectMenu);
    }
    // إضافة معالج قائمة اختيار تذاكر جديد
    const selectTicketActions = require('./components/selectMenus/selectTicketActions');
    client.selectMenus.set(selectTicketActions.id, selectTicketActions);
  }
}

// معالجة الأحداث
client.on('ready', async () => {
  console.log(`تم تسجيل الدخول باسم ${client.user.tag}!`);

  // الاتصال بقاعدة البيانات
  const dbConnected = await connectDB();
  if (dbConnected) {
    console.log('تم الاتصال بقاعدة البيانات MongoDB Atlas بنجاح!');
  } else {
    console.error('فشل الاتصال بقاعدة البيانات MongoDB Atlas!');
  }

  client.user.setActivity('/setup | /help', { type: 'PLAYING' });
});

// معالجة الرسائل
client.on('messageCreate', async message => {
  if (message.author.bot) return;

  // التحقق من بادئة الأمر
  if (!message.content.startsWith(config.prefix)) return;

  const args = message.content.slice(config.prefix.length).trim().split(/ +/);
  const commandName = args.shift().toLowerCase();

  const command = client.commands.get(commandName);

  if (!command) return;

  try {
    await command.execute(message, args, client);
  } catch (error) {
    console.error(error);
    const errorMessage = await client.translate(message.guild.id, 'errors.command_error');
    await message.reply(errorMessage);
  }
});

// معالجة التفاعلات (الأزرار، القوائم المنسدلة، إلخ)
client.on('interactionCreate', async interaction => {
  if (interaction.isCommand()) {
    const command = client.commands.get(interaction.commandName);

    if (!command) return;

    try {
      await command.execute(interaction, client);
    } catch (error) {
      console.error(error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.command_error');
      try {
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({ content: errorMessage });
        } else if (interaction.replied) {
          await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
      }
    }
  } else if (interaction.isButton()) {
    const button = client.buttons.get(interaction.customId);

    if (!button) {
      // معالجة خاصة لأزرار إنشاء التذاكر وإغلاقها
      if (interaction.customId.startsWith('create_ticket_')) {
        try {
          const { createTicket } = require('./utils/ticketManager');
          // استخراج معرف اللوحة من معرف الزر
          const panelId = interaction.customId.split('_')[2];
          await createTicket(interaction, client, panelId);
        } catch (error) {
          console.error('Error handling create_ticket button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_creation_error');
          try {
            if (interaction.deferred && !interaction.replied) {
              await interaction.editReply({ content: errorMessage });
            } else if (interaction.replied) {
              await interaction.followUp({ content: errorMessage, ephemeral: true });
            } else {
              await interaction.reply({ content: errorMessage, ephemeral: true });
            }
          } catch (replyError) {
            console.error('Failed to reply to interaction:', replyError);
          }
        }
        return;
      } else if (interaction.customId.startsWith('close_ticket_')) {
        try {
          const { closeTicket } = require('./utils/ticketManager');
          await closeTicket(interaction, client);
        } catch (error) {
          console.error('Error handling close_ticket button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_close_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('confirm_close_')) {
        try {
          const { confirmCloseTicket } = require('./utils/ticketManager');
          await confirmCloseTicket(interaction, client);
        } catch (error) {
          console.error('Error handling confirm_close button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_close_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('cancel_close_')) {
        try {
          const { cancelCloseTicket } = require('./utils/ticketManager');
          await cancelCloseTicket(interaction, client);
        } catch (error) {
          console.error('Error handling cancel_close button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_cancel_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('add_member_to_ticket_')) {
        try {
          const { addMemberToTicket } = require('./utils/ticketManager');
          await addMemberToTicket(interaction, client);
        } catch (error) {
          console.error('Error handling add_member_to_ticket button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_add_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('remove_member_from_ticket_')) {
        try {
          const { removeMemberFromTicket } = require('./utils/ticketManager');
          await removeMemberFromTicket(interaction, client);
        } catch (error) {
          console.error('Error handling remove_member_from_ticket button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_remove_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('delete_ticket_')) {
        try {
          const { deleteTicket } = require('./utils/ticketManager');
          await deleteTicket(interaction, client);
        } catch (error) {
          console.error('Error handling delete_ticket button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_delete_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId === 'confirm_delete_category') {
        try {
          const confirmDeleteCategory = require('./components/buttons/confirmDeleteCategory');
          await confirmDeleteCategory.execute(interaction, client);
        } catch (error) {
          console.error('Error handling confirm_delete_category button:', error);
          console.error(error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.category_delete_error') || 'حدث خطأ أثناء حذف الفئة';
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('confirm_delete_')) {
        try {
          const { confirmDeleteTicket } = require('./utils/ticketManager');
          await confirmDeleteTicket(interaction, client);
        } catch (error) {
          console.error('Error handling confirm_delete button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_delete_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('cancel_delete_')) {
        try {
          const { cancelDeleteTicket } = require('./utils/ticketManager');
          await cancelDeleteTicket(interaction, client);
        } catch (error) {
          console.error('Error handling cancel_delete button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_cancel_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('create_transcript_')) {
        try {
          const { createTranscript } = require('./utils/ticketManager');
          await createTranscript(interaction, client);
        } catch (error) {
          console.error('Error handling create_transcript button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.transcript_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('save_ticket_name_format_')) {
        try {
          const saveTicketNameFormat = require('./components/buttons/saveTicketNameFormat');
          await saveTicketNameFormat.execute(interaction, client);
        } catch (error) {
          console.error('Error handling save_ticket_name_format button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.database_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      } else if (interaction.customId.startsWith('back_to_panel_action_')) {
        try {
          const backToPanelAction = require('./components/buttons/backToPanelAction');
          await backToPanelAction.execute(interaction, client);
        } catch (error) {
          console.error('Error handling back_to_panel_action button:', error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.button_error');
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          }
        }
        return;
      }
      return;
    }

    try {
      await button.execute(interaction, client);
    } catch (error) {
      console.error(error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.button_error');
      try {
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({ content: errorMessage });
        } else if (interaction.replied) {
          await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
      }
    }
  } else if (interaction.isStringSelectMenu()) {
    const selectMenu = client.selectMenus.get(interaction.customId);

    if (!selectMenu) {
      // معالجة خاصة للقوائم المنسدلة
      if (interaction.customId.startsWith('create_ticket_')) {
        try {
          const { createTicket } = require('./utils/ticketManager');
          // استخراج معرف اللوحة من القيمة المحددة
          const panelId = interaction.values[0].split('_')[2];

          // تأجيل الرد على التفاعل لزيادة مهلة الاستجابة (إذا لم يتم الرد عليه بالفعل)
          if (!interaction.replied && !interaction.deferred) {
            await interaction.deferReply({ ephemeral: true });
          } else {
            console.log('التفاعل تم الرد عليه بالفعل أو تم تأجيله');
            return;
          }

          // إنشاء التذكرة
          const ticketChannel = await createTicket(interaction, client, panelId);

          // إعادة تحميل اللوحة لإزالة علامة الصح
          if (ticketChannel) {
            try {
              // الحصول على بيانات اللوحة
              const panel = await panelService.getPanelById(panelId);
              if (panel && panel.messageId && panel.channelId) {
                const channel = await client.channels.fetch(panel.channelId);
                if (channel) {
                  const message = await channel.messages.fetch(panel.messageId);
                  if (message) {
                    // إعادة إنشاء مكونات اللوحة
                    const { EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

                    // الحصول على الترجمات
                    const ticketDescription = await client.translate(interaction.guild.id, 'ticket.create_button');

                    // إعادة إنشاء مكونات اللوحة حسب نوع العرض
                    let row;

                    if (panel.displayType.startsWith('dropdown')) {
                      // إنشاء قائمة منسدلة
                      const options = panel.categories && panel.categories.length > 0
                        ? panel.categories.map(category => ({
                            label: category.name,
                            description: category.description,
                            value: `ticket_category_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`,
                            emoji: category.emoji
                          }))
                        : [{ label: ticketDescription, description: 'إنشاء تذكرة دعم جديدة', value: `create_ticket_${panel.panelId}`, emoji: '🎫' }];

                      row = new ActionRowBuilder()
                        .addComponents(
                          new StringSelectMenuBuilder()
                            .setCustomId(`create_ticket_${panel.panelId}`)
                            .setPlaceholder(ticketDescription)
                            .addOptions(options)
                        );
                    } else {
                      // إنشاء أزرار
                      const buttons = panel.categories && panel.categories.length > 0
                        ? panel.categories.map(category =>
                            new ButtonBuilder()
                              .setCustomId(`create_ticket_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`)
                              .setLabel(category.name)
                              .setStyle(ButtonStyle.Primary)
                              .setEmoji(category.emoji)
                          )
                        : [
                            new ButtonBuilder()
                              .setCustomId(`create_ticket_${panel.panelId}`)
                              .setLabel(ticketDescription)
                              .setStyle(ButtonStyle.Primary)
                              .setEmoji('🎫')
                          ];

                      row = new ActionRowBuilder().addComponents(buttons);
                    }

                    // تحديث الرسالة
                    if ((panel.displayType === 'dropdown_image' || panel.displayType === 'buttons_image') && panel.imageUrl) {
                      // تحديث الرسالة مع الصورة
                      try {
                        let filesToSend;
                        if (panel.imageUrl.startsWith('http://') || panel.imageUrl.startsWith('https://')) {
                          const imageBuffer = await fetchImageBuffer(panel.imageUrl);
                          filesToSend = [{ attachment: imageBuffer, name: 'panel_image.png' }];
                        } else {
                          filesToSend = [panel.imageUrl];
                        }
                        await message.edit({
                          files: filesToSend,
                          components: [row]
                        });
                      } catch (error) {
                        console.error('Error sending panel image:', error);
                        // إرسال الرسالة بدون الصورة في حال الخطأ
                        await message.edit({
                          components: [row]
                        });
                      }
                    } else {
                      // تحديث الرسالة مع الإمبد
                      const panelEmbed = new EmbedBuilder()
                        .setColor(client.config.embedColor)
                        .setTitle(`${panel.name}`)
                        .setDescription(panel.description || 'انقر على الزر أدناه لإنشاء تذكرة دعم.')
                        .setFooter({ text: client.config.footerText });

                      await message.edit({
                        embeds: [panelEmbed],
                        components: [row]
                      });
                    }
                  }
                }
              }
            } catch (refreshError) {
              console.error('Error refreshing panel after ticket creation:', refreshError);
            }
          }
        } catch (error) {
          console.error('Error handling create_ticket select menu:', error);

          // التحقق من صلاحية التفاعل قبل محاولة الرد
          try {
            const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_creation_error');
            if (interaction.deferred && !interaction.replied) {
              await interaction.editReply({ content: errorMessage });
            } else if (interaction.replied) {
              await interaction.followUp({ content: errorMessage, ephemeral: true });
            } else {
              await interaction.reply({ content: errorMessage, ephemeral: true });
            }
          } catch (replyError) {
            console.error('Failed to reply to interaction:', replyError);
          }
        }
        return;
      }

      const specialSelectMenus = {
        'select_display_type': './components/selectMenus/selectDisplayType',
        'select_support_roles_for_panel': './components/selectMenus/selectSupportRoles',
        'select_support_roles': './components/selectMenus/selectSupportRoles',
        'select_transcript_channel_for_panel': './components/selectMenus/selectTranscriptChannel',
        'select_ticket_category_for_panel': './components/selectMenus/selectTicketCategory',
        'select_channel_for_panel': './components/selectMenus/selectChannelForPanel',
        'select_category_action': './components/selectMenus/selectCategoryAction',
        'select_category_to_edit': './components/selectMenus/selectCategoryToEdit',
        'select_category_to_delete': './components/selectMenus/selectCategoryToDelete',
        'select_ticket_name_format': './components/selectMenus/selectTicketNameFormat',
        'select_panel_action_extra': './components/selectMenus/selectPanelAction'
      };

      const handlerPath = specialSelectMenus[interaction.customId];

      if (handlerPath) {
        try {
          const selectMenuHandler = require(handlerPath);
          await selectMenuHandler.execute(interaction, client);
        } catch (error) {
          console.error(`Error handling ${interaction.customId}:`, error);
          const errorMessage = await client.translate(interaction.guild.id, 'errors.menu_error');
          try {
            if (interaction.deferred && !interaction.replied) {
              await interaction.editReply({ content: errorMessage });
            } else if (interaction.replied) {
              await interaction.followUp({ content: errorMessage, ephemeral: true });
            } else {
              await interaction.reply({ content: errorMessage, ephemeral: true });
            }
          } catch (replyError) {
            console.error('Failed to reply to interaction:', replyError);
          }
        }
        return;
      }
      return;
    }

    try {
      await selectMenu.execute(interaction, client);
    } catch (error) {
      console.error(error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.menu_error');
      try {
        // التحقق مما إذا كان التفاعل قد تم الرد عليه بالفعل
        if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({ content: errorMessage });
        } else if (interaction.replied) {
          await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      } catch (replyError) {
        console.error('Failed to reply to interaction:', replyError);
      }
    }
  } else if (interaction.isModalSubmit()) {
    // معالجة النماذج
    const modalHandlers = {
      'panel_name_modal': './components/panelNameModal',
      'edit_panel_name_modal': './components/editPanelNameModal',
      'edit_panel_description_modal': './components/editPanelDescriptionModal',
      'edit_welcome_message_modal': './components/editWelcomeMessageModal',
      'add_category_modal': './components/addCategoryModal',
      'edit_category_modal': './components/editCategoryModal'
    };

    // معالجة نماذج إضافة وإزالة الأعضاء
    if (interaction.customId.startsWith('add_member_modal_')) {
      try {
        const { handleAddMemberModal } = require('./utils/ticketManager');
        await handleAddMemberModal(interaction, client);
      } catch (error) {
        console.error('Error handling add_member_modal:', error);
        const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_add_error');
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      }
      return;
    } else if (interaction.customId.startsWith('remove_member_modal_')) {
      try {
        const { handleRemoveMemberModal } = require('./utils/ticketManager');
        await handleRemoveMemberModal(interaction, client);
      } catch (error) {
        console.error('Error handling remove_member_modal:', error);
        const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_remove_error');
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      }
      return;
    }

    // معالجة نماذج رابط الصورة
    if (interaction.customId.startsWith('image_url_modal_')) {
      try {
        console.log('معالجة نموذج رابط الصورة:', interaction.customId);
        const modalHandler = require('./components/imageUrlModal');
        await modalHandler.execute(interaction, client);
      } catch (error) {
        console.error(`Error handling image URL modal:`, error);
        const errorMessage = await client.translate(interaction.guild.id, 'errors.modal_error');
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      }
      return;
    }

    const handlerPath = modalHandlers[interaction.customId];

    if (handlerPath) {
      try {
        const modalHandler = require(handlerPath);
        await modalHandler.execute(interaction, client);
      } catch (error) {
        console.error(`Error handling modal ${interaction.customId}:`, error);
        const errorMessage = await client.translate(interaction.guild.id, 'errors.modal_error');
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      }
    }
  }
});

// تسجيل الدخول إلى Discord
client.login(process.env.TOKEN);
