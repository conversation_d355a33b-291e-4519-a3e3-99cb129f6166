const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle, PermissionsBitField, ModalBuilder, TextInputBuilder, TextInputStyle, UserSelectMenuBuilder, StringSelectMenuBuilder } = require('discord.js');
const { panelService, ticketService } = require('../services');
const { sendLog } = require('./logManager');

// دالة للتحقق من صلاحيات فريق الدعم
async function checkSupportPermissions(interaction, client) {
  try {
    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(interaction.channel.id);

    if (!ticket) {
      return { hasPermission: false, error: 'لم يتم العثور على التذكرة.' };
    }

    // الحصول على معلومات اللوحة
    const panel = await panelService.getPanelById(ticket.panelId);

    if (!panel) {
      return { hasPermission: false, error: 'لم يتم العثور على اللوحة المرتبطة بالتذكرة.' };
    }

    // التحقق من أن المستخدم لديه أحد أدوار الدعم
    const userRoles = interaction.member.roles.cache.map(role => role.id);
    const hasSupportRole = panel.supportRoles.some(roleId => userRoles.includes(roleId));

    // التحقق من صلاحية الإدارة كبديل
    const hasAdminPermission = interaction.member.permissions.has(PermissionsBitField.Flags.Administrator);

    if (!hasSupportRole && !hasAdminPermission) {
      return {
        hasPermission: false,
        error: 'ليس لديك الصلاحية لاستخدام هذه الميزة. يجب أن تكون عضواً في فريق الدعم.'
      };
    }

    return { hasPermission: true, ticket, panel };
  } catch (error) {
    console.error('Error checking support permissions:', error);
    return { hasPermission: false, error: 'حدث خطأ أثناء التحقق من الصلاحيات.' };
  }
}

// إنشاء تذكرة جديدة
async function createTicket(interaction, client, panelId) {
  try {
    // التحقق من وجود تذكرة حالية مفتوحة للمستخدم في نفس السيرفر عبر الكاش
    const openTickets = interaction.guild.channels.cache.filter(channel => channel.name.startsWith('ticket-'));
    for (const [channelId, channel] of openTickets) {
      const ticket = await ticketService.getTicketByChannelId(channelId);
    if (ticket && ticket.userId === interaction.user.id && ticket.status === 'open') {
      const channelMention = `<#${channel.id}>`;
      if (!interaction.replied && !interaction.deferred) {
        const components = interaction.message ? interaction.message.components.map(row => {
          row.components = row.components.map(component => {
            component.setDisabled(false);
            return component;
          });
          return row;
        }) : [];
        await interaction.update({
          content: `لديك تذكرة حالية مفتوحة ${channelMention}. يرجى إغلاقها قبل فتح تذكرة جديدة.`,
          components,
          ephemeral: true
        });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({
          content: `لديك تذكرة حالية مفتوحة ${channelMention}. يرجى إغلاقها قبل فتح تذكرة جديدة.`
        });
      }
      return null;
    }
    }

    // الحصول على بيانات اللوحة من قاعدة البيانات
    const panel = await panelService.getPanelById(panelId);

    if (!panel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_creation_error');
      // التحقق من حالة التفاعل قبل الرد
      if (interaction.deferred) {
        return interaction.editReply({
          content: errorMessage
        });
      } else if (!interaction.replied) {
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }
      return null;
    }

    // التحقق من وجود فئة للتذاكر
    const category = interaction.guild.channels.cache.get(panel.ticketCategory);

    if (!category) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_category_error');
      // التحقق من حالة التفاعل قبل الرد
      if (interaction.deferred) {
        return interaction.editReply({
          content: errorMessage
        });
      } else if (!interaction.replied) {
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }
      return null;
    }

    // إنشاء اسم للتذكرة بناءً على تنسيق اسم التذكرة المحدد في اللوحة
    let ticketName;

    // التحقق من تنسيق اسم التذكرة المحدد في اللوحة
    if (panel.ticketNameFormat === 'numbered') {
      // الحصول على عدد التذاكر الحالية للسيرفر
      const tickets = await ticketService.getTicketsByGuild(interaction.guild.id);
      const ticketNumber = tickets.length + 1;
      ticketName = `ticket-${ticketNumber}`;
    } else {
      // استخدام اسم المستخدم (الافتراضي)
      ticketName = `ticket-${interaction.user.username.toLowerCase()}`;
    }

    console.log(`تنسيق اسم التذكرة: ${panel.ticketNameFormat}, اسم التذكرة: ${ticketName}`);

    // إنشاء أذونات القناة
    const channelPermissions = [
      {
        id: interaction.guild.id,
        deny: [PermissionsBitField.Flags.ViewChannel]
      },
      {
        id: interaction.user.id,
        allow: [
          PermissionsBitField.Flags.ViewChannel,
          PermissionsBitField.Flags.SendMessages,
          PermissionsBitField.Flags.ReadMessageHistory
        ]
      },
      {
        id: client.user.id,
        allow: [
          PermissionsBitField.Flags.ViewChannel,
          PermissionsBitField.Flags.SendMessages,
          PermissionsBitField.Flags.ManageChannels,
          PermissionsBitField.Flags.ReadMessageHistory,
          PermissionsBitField.Flags.EmbedLinks,
          PermissionsBitField.Flags.AttachFiles
        ]
      }
    ];

    // إضافة أدوار الدعم إلى أذونات القناة
    for (const roleId of panel.supportRoles) {
      const role = interaction.guild.roles.cache.get(roleId);
      if (!role) {
        console.warn(`Role with ID ${roleId} not found in guild cache, skipping permission overwrite.`);
        continue;
      }
      channelPermissions.push({
        id: roleId,
        allow: [
          PermissionsBitField.Flags.ViewChannel,
          PermissionsBitField.Flags.SendMessages,
          PermissionsBitField.Flags.ReadMessageHistory
        ]
      });
    }

    // إنشاء قناة التذكرة
    const ticketChannel = await interaction.guild.channels.create({
      name: ticketName,
      type: 0, // TextChannel
      parent: category.id,
      permissionOverwrites: channelPermissions,
      topic: `تذكرة دعم لـ ${interaction.user.tag} | ID: ${interaction.user.id}`
    });

    // الحصول على رسالة الترحيب المخصصة من اللوحة
    let welcomeMessage = panel.welcomeMessage || 'مرحبًا {user}،\n\nشكرًا لإنشاء تذكرة دعم. سيقوم فريق الدعم لدينا بالرد عليك في أقرب وقت ممكن.\n\nيرجى وصف مشكلتك أو استفسارك بالتفصيل.';

    // استبدال المتغيرات في رسالة الترحيب
    welcomeMessage = welcomeMessage.replace('{user}', interaction.user.toString());

    // الحصول على الترجمات
    const welcomeTitle = await client.translate(interaction.guild.id, 'ticket.welcome.title');
    const closeButtonLabel = await client.translate(interaction.guild.id, 'ticket.close_button');
    const addMemberButtonLabel = await client.translate(interaction.guild.id, 'ticket.add_member_button');
    const removeMemberButtonLabel = await client.translate(interaction.guild.id, 'ticket.remove_member_button');

    // إنشاء رسالة الترحيب
    const welcomeEmbed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(welcomeTitle)
      .setDescription(welcomeMessage)
      .setFooter({ text: client.config.footerText });

    // إنشاء قائمة اختيار واحدة بدلاً من الأزرار
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('ticket_actions')
      .setPlaceholder('اختر إجراءً')
      .addOptions([
        {
          label: addMemberButtonLabel,
          description: 'إضافة عضو إلى التذكرة',
          value: 'add_member',
          emoji: '👥'
        },
        {
          label: removeMemberButtonLabel,
          description: 'إزالة عضو من التذكرة',
          value: 'remove_member',
          emoji: '🚫'
        },
        {
          label: closeButtonLabel,
          description: 'إغلاق التذكرة',
          value: 'close_ticket',
          emoji: '🔒'
        },
        {
          label: 'إرسال تذكير',
          description: 'إرسال تذكير للعضو في الخاص',
          value: 'send_reminder',
          emoji: '⏰'
        }
      ]);

    const actionRow = new ActionRowBuilder().addComponents(selectMenu);

    // إرسال رسالة الترحيب في قناة التذكرة مع قائمة الاختيار
    await ticketChannel.send({ embeds: [welcomeEmbed], components: [actionRow] });

    // الحصول على ترجمة الإشعار
    const ticketCreatedMessage = await client.translate(interaction.guild.id, 'ticket.created', { channel: ticketChannel.toString() });

    // إرسال إشعار للمستخدم
    try {
      if (interaction.deferred) {
        await interaction.editReply({
          content: ticketCreatedMessage
        });
      } else if (!interaction.replied) {
        await interaction.reply({
          content: ticketCreatedMessage,
          ephemeral: true
        });
      }
    } catch (error) {
      console.error('Error replying to interaction:', error);
    }

    // حفظ بيانات التذكرة في قاعدة البيانات
    const ticket = await ticketService.createTicket({
      guildId: interaction.guild.id,
      panelId: panelId,
      channelId: ticketChannel.id,
      userId: interaction.user.id
    });

    if (ticket) {
      console.log(`تم إنشاء تذكرة جديدة بمعرف: ${ticket.ticketId}`);

      // إرسال سجل إلى قناة النسخ
      await sendLog(client, interaction.guild.id, panelId, 'ticket_created', {
        user: interaction.user,
        channel: ticketChannel
      });
    } else {
      console.error('فشل حفظ التذكرة في قاعدة البيانات');
    }

    return ticketChannel;
  } catch (error) {
    console.error('Error creating ticket:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_creation_error');

    try {
      if (interaction.deferred) {
        await interaction.editReply({
          content: errorMessage
        });
      } else if (!interaction.replied) {
        await interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }
    } catch (replyError) {
      console.error('Error replying to interaction in catch block:', replyError);
    }

    return null;
  }
}

// إغلاق تذكرة
async function closeTicket(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;

    // التحقق من أن القناة هي قناة تذكرة
    if (!channel.name.startsWith('ticket-')) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.invalid_ticket');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.warn('لم يتم العثور على التذكرة في قاعدة البيانات');
    }

    // الحصول على الترجمات
    const confirmTitle = await client.translate(interaction.guild.id, 'ticket.close_confirmation.title');
    const confirmDescription = await client.translate(interaction.guild.id, 'ticket.close_confirmation.description');
    const confirmButton = await client.translate(interaction.guild.id, 'ticket.close_confirmation.confirm_button');
    const cancelButton = await client.translate(interaction.guild.id, 'ticket.close_confirmation.cancel_button');

    // إنشاء رسالة تأكيد
    const confirmEmbed = new EmbedBuilder()
      .setColor('#ff9900')
      .setTitle(confirmTitle)
      .setDescription(confirmDescription);

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`confirm_close_${channel.id}`)
          .setLabel(confirmButton)
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId(`cancel_close_${channel.id}`)
          .setLabel(cancelButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.reply({ embeds: [confirmEmbed], components: [row] });
  } catch (error) {
    console.error('Error closing ticket:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_close_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// تأكيد إغلاق التذكرة
async function confirmCloseTicket(interaction, client) {
  try {
    const channel = interaction.channel;

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.error('لم يتم العثور على التذكرة في قاعدة البيانات');
    } else {
      // إزالة صاحب التذكرة من القناة
      try {
        await channel.permissionOverwrites.delete(ticket.userId);
        console.log(`تم إزالة صاحب التذكرة ${ticket.userId} من القناة ${channel.name}`);
      } catch (error) {
        console.error('Error removing ticket owner from channel:', error);
      }

      // تحديث حالة التذكرة في قاعدة البيانات
      await ticketService.closeTicket(ticket.ticketId, interaction.user.id);

      // إرسال سجل إلى قناة النسخ
      await sendLog(client, interaction.guild.id, ticket.panelId, 'ticket_closed', {
        user: interaction.user,
        channel: channel,
        ticketOwner: { id: ticket.userId }
      });
    }

    // الحصول على الترجمات
    const closingTitle = await client.translate(interaction.guild.id, 'ticket.closing.title');
    const closingDescription = await client.translate(interaction.guild.id, 'ticket.closing.description');
    const deleteButtonLabel = await client.translate(interaction.guild.id, 'ticket.delete_ticket_button');
    const transcriptButtonLabel = await client.translate(interaction.guild.id, 'ticket.create_transcript_button');
    const reopenButtonLabel = 'إعادة فتح التذكرة';

    // إنشاء رسالة إغلاق مع معلومات إضافية
    let enhancedDescription = closingDescription;
    if (ticket) {
      enhancedDescription += `\n\nيمكنك اعادة فتح التذكرة عبر الضغط على اعادة فتح التذكرة`;
    }

    const closingEmbed = new EmbedBuilder()
      .setColor('#ff0000')
      .setTitle(closingTitle)
      .setDescription(enhancedDescription);

    // إنشاء أزرار ما بعد الإغلاق
    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`reopen_ticket_${channel.id}`)
          .setLabel(reopenButtonLabel)
          .setStyle(ButtonStyle.Success)
          .setEmoji('🔓'),
        new ButtonBuilder()
          .setCustomId(`delete_ticket_${channel.id}`)
          .setLabel(deleteButtonLabel)
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️'),
        new ButtonBuilder()
          .setCustomId(`create_transcript_${channel.id}`)
          .setLabel(transcriptButtonLabel)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📝')
      );

    // محاولة تحديث التفاعل مع معالجة أفضل للأخطاء
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.update({ embeds: [closingEmbed], components: [row] });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({ embeds: [closingEmbed], components: [row] });
      } else {
        await interaction.followUp({ embeds: [closingEmbed], components: [row], ephemeral: true });
      }
    } catch (updateError) {
      console.error('Error updating interaction in confirmCloseTicket:', updateError);
      // محاولة أخيرة للرد
      try {
        if (!interaction.replied) {
          await interaction.reply({ embeds: [closingEmbed], components: [row], ephemeral: true });
        }
      } catch (fallbackError) {
        console.error('Error in fallback reply for confirmCloseTicket:', fallbackError);
      }
    }
  } catch (error) {
    console.error('Error confirming ticket close:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_close_error');

    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({
          content: errorMessage
        });
      } else {
        await interaction.followUp({
          content: errorMessage,
          ephemeral: true
        });
      }
    } catch (replyError) {
      console.error('Error replying to interaction in error handler:', replyError);
    }
  }
}

// إلغاء إغلاق التذكرة
async function cancelCloseTicket(interaction, client) {
  try {
    // الحصول على الترجمات
    const cancelTitle = await client.translate(interaction.guild.id, 'ticket.canceled.title');
    const cancelDescription = await client.translate(interaction.guild.id, 'ticket.canceled.description');

    const cancelEmbed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(cancelTitle)
      .setDescription(cancelDescription);

    // محاولة تحديث التفاعل مع معالجة أفضل للأخطاء
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.update({ embeds: [cancelEmbed], components: [] });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({ embeds: [cancelEmbed], components: [] });
      } else {
        await interaction.followUp({ embeds: [cancelEmbed], components: [], ephemeral: true });
      }
    } catch (updateError) {
      console.error('Error updating interaction in cancelCloseTicket:', updateError);
      // محاولة أخيرة للرد
      try {
        if (!interaction.replied) {
          await interaction.reply({ embeds: [cancelEmbed], components: [], ephemeral: true });
        }
      } catch (fallbackError) {
        console.error('Error in fallback reply for cancelCloseTicket:', fallbackError);
      }
    }
  } catch (error) {
    console.error('Error canceling ticket close:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_cancel_error');

    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({
          content: errorMessage
        });
      } else {
        await interaction.followUp({
          content: errorMessage,
          ephemeral: true
        });
      }
    } catch (replyError) {
      console.error('Error replying to interaction in cancelCloseTicket error handler:', replyError);
    }
  }
}

// إعادة فتح التذكرة
async function reopenTicket(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;
    const ticket = permissionCheck.ticket;

    if (!ticket) {
      return interaction.reply({
        content: 'لم يتم العثور على معلومات التذكرة.',
        ephemeral: true
      });
    }

    // إعادة إضافة صاحب التذكرة إلى القناة
    try {
      await channel.permissionOverwrites.create(ticket.userId, {
        ViewChannel: true,
        SendMessages: true,
        ReadMessageHistory: true
      });
      console.log(`تم إعادة إضافة صاحب التذكرة ${ticket.userId} إلى القناة ${channel.name}`);
    } catch (error) {
      console.error('Error adding ticket owner back to channel:', error);
    }

    // تحديث حالة التذكرة في قاعدة البيانات لتصبح مفتوحة
    await ticketService.reopenTicket(ticket.ticketId);

    // إرسال سجل إلى قناة النسخ
    await sendLog(client, interaction.guild.id, ticket.panelId, 'ticket_reopened', {
      user: interaction.user,
      channel: channel,
      ticketOwner: { id: ticket.userId }
    });

    // الحصول على الترجمات
    const reopenTitle = 'تم إعادة فتح التذكرة';
    const reopenDescription = `تم إعادة فتح التذكرة بواسطة ${interaction.user}\n\n✅ تم إعادة إضافة صاحب التذكرة <@${ticket.userId}> إلى القناة.`;

    // إنشاء رسالة إعادة الفتح
    const reopenEmbed = new EmbedBuilder()
      .setColor('#00ff00')
      .setTitle(reopenTitle)
      .setDescription(reopenDescription);

    // إنشاء قائمة إجراءات التذكرة
    const ticketDescription = 'اختر إجراءً';
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('ticket_actions')
      .setPlaceholder(ticketDescription)
      .addOptions([
        {
          label: 'إضافة عضو',
          description: 'إضافة عضو إلى التذكرة',
          value: 'add_member',
          emoji: '👥'
        },
        {
          label: 'إزالة عضو',
          description: 'إزالة عضو من التذكرة',
          value: 'remove_member',
          emoji: '🚫'
        },
        {
          label: 'إغلاق التذكرة',
          description: 'إغلاق التذكرة',
          value: 'close_ticket',
          emoji: '🔒'
        },
        {
          label: 'إرسال تذكير',
          description: 'إرسال تذكير للعضو في الخاص',
          value: 'send_reminder',
          emoji: '⏰'
        }
      ]);

    const actionRow = new ActionRowBuilder().addComponents(selectMenu);

    // محاولة تحديث التفاعل مع معالجة أفضل للأخطاء
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.update({ embeds: [reopenEmbed], components: [actionRow] });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({ embeds: [reopenEmbed], components: [actionRow] });
      } else {
        await interaction.followUp({ embeds: [reopenEmbed], components: [actionRow], ephemeral: true });
      }
    } catch (updateError) {
      console.error('Error updating interaction in reopenTicket:', updateError);
      // محاولة أخيرة للرد
      try {
        if (!interaction.replied) {
          await interaction.reply({ embeds: [reopenEmbed], components: [actionRow], ephemeral: true });
        }
      } catch (fallbackError) {
        console.error('Error in fallback reply for reopenTicket:', fallbackError);
      }
    }
  } catch (error) {
    console.error('Error reopening ticket:', error);

    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: 'حدث خطأ أثناء إعادة فتح التذكرة.',
          ephemeral: true
        });
      } else if (interaction.deferred && !interaction.replied) {
        await interaction.editReply({
          content: 'حدث خطأ أثناء إعادة فتح التذكرة.'
        });
      } else {
        await interaction.followUp({
          content: 'حدث خطأ أثناء إعادة فتح التذكرة.',
          ephemeral: true
        });
      }
    } catch (replyError) {
      console.error('Error replying to interaction in reopenTicket error handler:', replyError);
    }
  }
}

// إضافة عضو إلى التذكرة
async function addMemberToTicket(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;

    // التحقق من أن القناة هي قناة تذكرة
    if (!channel.name.startsWith('ticket-')) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.invalid_ticket');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'ticket.add_member.title');
    const inputLabel = await client.translate(interaction.guild.id, 'ticket.add_member.input_label');
    const inputPlaceholder = await client.translate(interaction.guild.id, 'ticket.add_member.input_placeholder');

    // إنشاء نموذج لإدخال معرف العضو
    const modal = new ModalBuilder()
      .setCustomId(`add_member_modal_${channel.id}`)
      .setTitle(title);

    // إنشاء حقل إدخال معرف العضو
    const userIdInput = new TextInputBuilder()
      .setCustomId('user_id_input')
      .setLabel(inputLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(inputPlaceholder)
      .setRequired(true);

    const firstActionRow = new ActionRowBuilder().addComponents(userIdInput);
    modal.addComponents(firstActionRow);

    await interaction.showModal(modal);
  } catch (error) {
    console.error('Error adding member to ticket:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_add_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// معالجة نموذج إضافة العضو
async function handleAddMemberModal(interaction, client) {
  try {
    const channel = interaction.channel;
    const userId = interaction.fields.getTextInputValue('user_id_input');

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.error('لم يتم العثور على التذكرة في قاعدة البيانات');
      return interaction.reply({ content: 'حدث خطأ أثناء إضافة العضو.', ephemeral: true });
    }

    // التحقق من صحة معرف المستخدم
    try {
      // محاولة الحصول على المستخدم من Discord
      const user = await client.users.fetch(userId).catch(() => null);

      if (!user) {
        return interaction.reply({ content: 'معرف المستخدم غير صالح. يرجى التأكد من إدخال معرف صحيح.', ephemeral: true });
      }

      // إضافة العضو إلى القناة
      await channel.permissionOverwrites.create(userId, {
        ViewChannel: true,
        SendMessages: true,
        ReadMessageHistory: true
      });

      // الحصول على ترجمة رسالة النجاح
      const successMessage = await client.translate(interaction.guild.id, 'ticket.add_member.success', { user: `<@${userId}>` });

      // إرسال سجل إلى قناة النسخ
      await sendLog(client, interaction.guild.id, ticket.panelId, 'member_added', {
        user: interaction.user,
        member: { id: userId },
        channel: channel
      });

      await interaction.reply({ content: successMessage, ephemeral: true });
    } catch (error) {
      console.error('Error fetching user:', error);
      await interaction.reply({ content: 'حدث خطأ أثناء محاولة إضافة العضو. يرجى التأكد من إدخال معرف صحيح.', ephemeral: true });
    }
  } catch (error) {
    console.error('Error handling add member modal:', error);
    await interaction.reply({ content: 'حدث خطأ أثناء إضافة العضو.', ephemeral: true });
  }
}

// إزالة عضو من التذكرة
async function removeMemberFromTicket(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;

    // التحقق من أن القناة هي قناة تذكرة
    if (!channel.name.startsWith('ticket-')) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.invalid_ticket');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'ticket.remove_member.title');
    const inputLabel = await client.translate(interaction.guild.id, 'ticket.remove_member.input_label');
    const inputPlaceholder = await client.translate(interaction.guild.id, 'ticket.remove_member.input_placeholder');

    // إنشاء نموذج لإدخال معرف العضو
    const modal = new ModalBuilder()
      .setCustomId(`remove_member_modal_${channel.id}`)
      .setTitle(title);

    // إنشاء حقل إدخال معرف العضو
    const userIdInput = new TextInputBuilder()
      .setCustomId('user_id_input')
      .setLabel(inputLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(inputPlaceholder)
      .setRequired(true);

    const firstActionRow = new ActionRowBuilder().addComponents(userIdInput);
    modal.addComponents(firstActionRow);

    await interaction.showModal(modal);
  } catch (error) {
    console.error('Error removing member from ticket:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_member_remove_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// معالجة نموذج إزالة العضو
async function handleRemoveMemberModal(interaction, client) {
  try {
    const channel = interaction.channel;
    const userId = interaction.fields.getTextInputValue('user_id_input');

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.error('لم يتم العثور على التذكرة في قاعدة البيانات');
      return interaction.reply({ content: 'حدث خطأ أثناء إزالة العضو.', ephemeral: true });
    }

    // التحقق من أن العضو المحدد ليس صاحب التذكرة
    if (userId === ticket.userId) {
      return interaction.reply({ content: 'لا يمكن إزالة صاحب التذكرة.', ephemeral: true });
    }

    // التحقق من صحة معرف المستخدم
    try {
      // محاولة الحصول على المستخدم من Discord
      const user = await client.users.fetch(userId).catch(() => null);

      if (!user) {
        return interaction.reply({ content: 'معرف المستخدم غير صالح. يرجى التأكد من إدخال معرف صحيح.', ephemeral: true });
      }

      // إزالة العضو من القناة
      await channel.permissionOverwrites.delete(userId);

      // الحصول على ترجمة رسالة النجاح
      const successMessage = await client.translate(interaction.guild.id, 'ticket.remove_member.success', { user: `<@${userId}>` });

      // إرسال سجل إلى قناة النسخ
      await sendLog(client, interaction.guild.id, ticket.panelId, 'member_removed', {
        user: interaction.user,
        member: { id: userId },
        channel: channel
      });

      await interaction.reply({ content: successMessage, ephemeral: true });
    } catch (error) {
      console.error('Error fetching user:', error);
      await interaction.reply({ content: 'حدث خطأ أثناء محاولة إزالة العضو. يرجى التأكد من إدخال معرف صحيح.', ephemeral: true });
    }
  } catch (error) {
    console.error('Error handling remove member modal:', error);
    await interaction.reply({ content: 'حدث خطأ أثناء إزالة العضو.', ephemeral: true });
  }
}

// حذف التذكرة
async function deleteTicket(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;

    // التحقق من أن القناة هي قناة تذكرة
    if (!channel.name.startsWith('ticket-')) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.invalid_ticket');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.error('لم يتم العثور على التذكرة في قاعدة البيانات');
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'ticket.delete.title');
    const description = await client.translate(interaction.guild.id, 'ticket.delete.description');
    const confirmButton = await client.translate(interaction.guild.id, 'ticket.delete.confirm_button');
    const cancelButton = await client.translate(interaction.guild.id, 'ticket.delete.cancel_button');

    // إنشاء رسالة تأكيد
    const confirmEmbed = new EmbedBuilder()
      .setColor('#ff0000')
      .setTitle(title)
      .setDescription(description);

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`confirm_delete_${channel.id}`)
          .setLabel(confirmButton)
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId(`cancel_delete_${channel.id}`)
          .setLabel(cancelButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.reply({ embeds: [confirmEmbed], components: [row] });
  } catch (error) {
    console.error('Error deleting ticket:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_delete_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// تأكيد حذف التذكرة
async function confirmDeleteTicket(interaction, client) {
  try {
    const channel = interaction.channel;

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (ticket) {
      // إرسال سجل إلى قناة النسخ قبل حذف القناة
      await sendLog(client, interaction.guild.id, ticket.panelId, 'ticket_deleted', {
        user: interaction.user,
        channelName: channel.name
      });

      // حذف التذكرة من قاعدة البيانات
      await ticketService.deleteTicket(ticket.ticketId);
    }

    // الحصول على ترجمة رسالة النجاح
    const successMessage = await client.translate(interaction.guild.id, 'ticket.delete.success');

    await interaction.update({ content: successMessage, embeds: [], components: [] });

    // حذف القناة بعد ثانيتين
    setTimeout(async () => {
      try {
        await channel.delete();
      } catch (error) {
        console.error('Error deleting ticket channel:', error);
      }
    }, 2000);
  } catch (error) {
    console.error('Error confirming ticket deletion:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_delete_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// إلغاء حذف التذكرة
async function cancelDeleteTicket(interaction, client) {
  try {
    await interaction.update({ content: 'تم إلغاء حذف التذكرة.', embeds: [], components: [] });
  } catch (error) {
    console.error('Error canceling ticket deletion:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.ticket_cancel_error');
    await interaction.reply({
      content: errorMessage,
      ephemeral: true
    });
  }
}

// إنشاء نسخة من التذكرة
async function createTranscript(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;

    // التحقق من أن القناة هي قناة تذكرة
    if (!channel.name.startsWith('ticket-')) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.invalid_ticket');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // البحث عن التذكرة في قاعدة البيانات
    const ticket = await ticketService.getTicketByChannelId(channel.id);

    if (!ticket) {
      console.error('لم يتم العثور على التذكرة في قاعدة البيانات');
      return interaction.reply({
        content: 'حدث خطأ أثناء إنشاء نسخة من التذكرة.',
        ephemeral: true
      });
    }

    // الحصول على بيانات اللوحة
    const panel = await panelService.getPanelById(ticket.panelId);

    if (!panel || !panel.transcriptChannel) {
      return interaction.reply({
        content: 'لم يتم تحديد قناة النسخ للوحة.',
        ephemeral: true
      });
    }

    // الحصول على قناة النسخ
    const transcriptChannel = await client.channels.fetch(panel.transcriptChannel).catch(() => null);

    if (!transcriptChannel) {
      return interaction.reply({
        content: 'لم يتم العثور على قناة النسخ.',
        ephemeral: true
      });
    }

    // إظهار رسالة انتظار للمستخدم
    await interaction.reply({
      content: 'جاري إنشاء نسخة من التذكرة... يرجى الانتظار.',
      ephemeral: true
    });

    // الحصول على جميع الرسائل في القناة
    const messages = await channel.messages.fetch({ limit: 100 });
    let allMessages = [...messages.values()];

    // استمر في جلب الرسائل إذا كان هناك المزيد
    let lastMessageId = allMessages[allMessages.length - 1]?.id;

    while (lastMessageId && allMessages.length < 1000) { // حد أقصى 1000 رسالة
      const moreMessages = await channel.messages.fetch({
        limit: 100,
        before: lastMessageId
      });

      if (moreMessages.size === 0) break;

      const newMessages = [...moreMessages.values()];
      allMessages = [...allMessages, ...newMessages];
      lastMessageId = newMessages[newMessages.length - 1]?.id;
    }

    // ترتيب الرسائل من الأقدم إلى الأحدث
    allMessages.reverse();

    // إنشاء نص النسخة
    let transcriptText = `# نسخة من التذكرة: ${channel.name}\n`;
    transcriptText += `تاريخ الإنشاء: ${new Date(ticket.createdAt).toLocaleString()}\n`;
    transcriptText += `صاحب التذكرة: <@${ticket.userId}>\n\n`;
    transcriptText += `=== محتوى التذكرة ===\n\n`;

    // إضافة الرسائل إلى النص
    for (const msg of allMessages) {
      const timestamp = new Date(msg.createdTimestamp).toLocaleString();
      let content = msg.content || '';

      // إضافة المرفقات إلى المحتوى
      if (msg.attachments.size > 0) {
        content += '\n\nالمرفقات:\n';
        msg.attachments.forEach(attachment => {
          content += `- ${attachment.url}\n`;
        });
      }

      // إضافة الصور المضمنة إلى المحتوى
      if (msg.embeds.length > 0) {
        content += '\n\nمحتوى مضمن:\n';
        msg.embeds.forEach(embed => {
          if (embed.title) content += `عنوان: ${embed.title}\n`;
          if (embed.description) content += `وصف: ${embed.description}\n`;
          if (embed.image) content += `صورة: ${embed.image.url}\n`;
        });
      }

      transcriptText += `[${timestamp}] ${msg.author.tag} (${msg.author.id}):\n${content || '(بدون محتوى)'}\n\n`;
    }

    // إضافة معلومات إضافية
    transcriptText += `\n=== معلومات إضافية ===\n`;
    transcriptText += `عدد الرسائل: ${allMessages.length}\n`;
    if (ticket.closedAt) {
      transcriptText += `تاريخ الإغلاق: ${new Date(ticket.closedAt).toLocaleString()}\n`;
      transcriptText += `تم إغلاقها بواسطة: <@${ticket.closedBy}>\n`;
    }

    // إنشاء ملف النسخة
    const transcriptBuffer = Buffer.from(transcriptText, 'utf-8');

    // إرسال النسخة إلى قناة النسخ
    await transcriptChannel.send({
      content: `نسخة من التذكرة: ${channel.name} | صاحب التذكرة: <@${ticket.userId}>`,
      files: [{
        attachment: transcriptBuffer,
        name: `transcript-${channel.name}.txt`
      }]
    });

    // الحصول على ترجمة رسالة النجاح
    const successMessage = await client.translate(interaction.guild.id, 'ticket.transcript.success');

    await interaction.editReply({
      content: successMessage,
      ephemeral: true
    });
  } catch (error) {
    console.error('Error creating transcript:', error);
    const errorMessage = await client.translate(interaction.guild.id, 'errors.transcript_error');

    if (interaction.deferred || interaction.replied) {
      await interaction.editReply({
        content: errorMessage,
        ephemeral: true
      });
    } else {
      await interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }
  }
}

// إرسال تذكير للعضو
async function sendReminder(interaction, client) {
  try {
    // التحقق من صلاحيات فريق الدعم
    const permissionCheck = await checkSupportPermissions(interaction, client);

    if (!permissionCheck.hasPermission) {
      return interaction.reply({
        content: permissionCheck.error,
        ephemeral: true
      });
    }

    const channel = interaction.channel;
    const ticket = permissionCheck.ticket;

    // الحصول على صاحب التذكرة
    const ticketOwner = await client.users.fetch(ticket.userId).catch(() => null);

    if (!ticketOwner) {
      return interaction.reply({
        content: 'لم يتم العثور على صاحب التذكرة.',
        ephemeral: true
      });
    }

    // إرسال رسالة تذكير في الخاص
    try {
      const reminderMessage = `تذكير: لديك تذكرة دعم مفتوحة في سيرفر **${interaction.guild.name}**\nرابط التذكرة: <#${channel.id}>`;

      await ticketOwner.send(reminderMessage);

      // إرسال رسالة تأكيد في التذكرة
      await interaction.reply({
        content: `تم إرسال تذكير إلى <@${ticket.userId}> في الخاص.`,
        ephemeral: true
      });

      // إرسال سجل إلى قناة النسخ
      await sendLog(client, interaction.guild.id, ticket.panelId, 'reminder_sent', {
        user: interaction.user,
        ticketOwner: ticketOwner,
        channel: channel
      });
    } catch (error) {
      console.error('Error sending reminder DM:', error);
      await interaction.reply({
        content: 'لم أتمكن من إرسال رسالة خاصة للعضو. قد يكون لديه الرسائل الخاصة مغلقة.',
        ephemeral: true
      });
    }
  } catch (error) {
    console.error('Error sending reminder:', error);
    await interaction.reply({
      content: 'حدث خطأ أثناء إرسال التذكير.',
      ephemeral: true
    });
  }
}

// تصدير وظائف إدارة التذاكر
module.exports = {
  createTicket,
  closeTicket,
  confirmCloseTicket,
  cancelCloseTicket,
  reopenTicket,
  addMemberToTicket,
  removeMemberFromTicket,
  handleAddMemberModal,
  handleRemoveMemberModal,
  deleteTicket,
  confirmDeleteTicket,
  cancelDeleteTicket,
  createTranscript,
  sendReminder,
  checkSupportPermissions
};
