const { setupStates } = require('./buttons');

// معالجة اختيار أدوار فريق الدعم
async function handleSelectSupportRoles(interaction, client) {
  const state = setupStates.get(interaction.user.id);
  
  if (!state) {
    return interaction.reply({
      content: 'حدث خطأ في حالة الإعداد. يرجى بدء الإعداد مرة أخرى باستخدام الأمر `/setup`.',
      ephemeral: true
    });
  }
  
  // تحديث أدوار الدعم المحددة
  state.supportRoles = interaction.values;
  setupStates.set(interaction.user.id, state);
  
  // إعادة عرض الخطوة الحالية مع القيم المحدثة
  const { showStep2 } = require('../commands/setup');
  await showStep2(interaction, client, state);
}

// معالجة اختيار فئة التذاكر
async function handleSelectTicketCategory(interaction, client) {
  const state = setupStates.get(interaction.user.id);
  
  if (!state) {
    return interaction.reply({
      content: 'حدث خطأ في حالة الإعداد. يرجى بدء الإعداد مرة أخرى باستخدام الأمر `/setup`.',
      ephemeral: true
    });
  }
  
  // تحديث فئة التذاكر المحددة
  state.ticketCategory = interaction.values[0];
  setupStates.set(interaction.user.id, state);
  
  // إعادة عرض الخطوة الحالية مع القيم المحدثة
  const { showStep3 } = require('../commands/setup');
  await showStep3(interaction, client, state);
}

// معالجة اختيار قناة النسخ
async function handleSelectTranscriptChannel(interaction, client) {
  const state = setupStates.get(interaction.user.id);
  
  if (!state) {
    return interaction.reply({
      content: 'حدث خطأ في حالة الإعداد. يرجى بدء الإعداد مرة أخرى باستخدام الأمر `/setup`.',
      ephemeral: true
    });
  }
  
  // تحديث قناة النسخ المحددة
  state.transcriptChannel = interaction.values[0];
  setupStates.set(interaction.user.id, state);
  
  // إعادة عرض الخطوة الحالية مع القيم المحدثة
  const { showStep4 } = require('../commands/setup');
  await showStep4(interaction, client, state);
}

// معالجة اختيار قناة الهدف
async function handleSelectTargetChannel(interaction, client) {
  const state = setupStates.get(interaction.user.id);
  
  if (!state) {
    return interaction.reply({
      content: 'حدث خطأ في حالة الإعداد. يرجى بدء الإعداد مرة أخرى باستخدام الأمر `/setup`.',
      ephemeral: true
    });
  }
  
  // تحديث قناة الهدف المحددة
  state.targetChannel = interaction.values[0];
  setupStates.set(interaction.user.id, state);
  
  // إعادة عرض الخطوة الحالية مع القيم المحدثة
  const { showStep5 } = require('../commands/setup');
  await showStep5(interaction, client, state);
}

// تصدير معالجات القوائم المنسدلة
module.exports = {
  handleSelectSupportRoles,
  handleSelectTicketCategory,
  handleSelectTranscriptChannel,
  handleSelectTargetChannel
};
