const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'create_panel',

  async execute(interaction, client) {
    // إنشاء حالة إعداد جديدة للمستخدم
    setupStates.set(interaction.user.id, {
      step: 1,
      panelName: 'New Panel',
      supportRoles: [],
      ticketCategory: null,
      transcriptChannel: null,
      targetChannel: null
    });

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'setup.steps.step1.title');
    const description = await client.translate(interaction.guild.id, 'setup.steps.step1.description');
    const currentName = await client.translate(interaction.guild.id, 'setup.steps.step1.current_name');
    const setNameButton = await client.translate(interaction.guild.id, 'setup.steps.step1.set_name_button');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

    // عرض الخطوة الأولى
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    embed.addFields({ name: currentName, value: 'New Panel' });

    const row1 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('set_panel_name')
          .setLabel(setNameButton)
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🔄')
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('setup_continue')
          .setLabel(continueButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
