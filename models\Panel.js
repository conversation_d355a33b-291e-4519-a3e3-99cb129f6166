const mongoose = require('mongoose');

// نموذج بيانات لوحة التذاكر
const PanelSchema = new mongoose.Schema({
  panelId: {
    type: String,
    required: true,
    unique: true
  },
  guildId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    default: 'New Panel'
  },
  description: {
    type: String,
    default: ''
  },
  welcomeMessage: {
    type: String,
    default: ''
  },
  displayType: {
    type: String,
    enum: ['dropdown_image', 'dropdown_message', 'buttons_image', 'buttons_message'],
    default: 'dropdown_message'
  },
  imageUrl: {
    type: String,
    default: ''
  },
  categories: [{
    name: String,
    description: String,
    emoji: String
  }],
  supportRoles: [{
    type: String
  }],
  ticketCategory: {
    type: String
  },
  transcriptChannel: {
    type: String
  },
  ticketNameFormat: {
    type: String,
    enum: ['username', 'numbered'],
    default: 'username'
  },
  messageId: {
    type: String
  },
  channelId: {
    type: String
  },
  createdBy: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// تحديث تاريخ التعديل قبل الحفظ
PanelSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Panel', PanelSchema);
