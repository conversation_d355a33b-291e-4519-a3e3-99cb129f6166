const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'setup_back',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الرجوع إلى الخطوة السابقة
    if (state.step > 1) {
      state.step--;
      setupStates.set(interaction.user.id, state);

      // عرض الخطوة المناسبة
      switch (state.step) {
        case 1:
          await showStep1(interaction, client, state);
          break;
        case 2:
          await showStep2(interaction, client, state);
          break;
        case 3:
          await showStep3(interaction, client, state);
          break;
        case 4:
          await showStep4(interaction, client, state);
          break;
      }
    } else {
      // الحصول على الترجمات
      const title = await client.translate(interaction.guild.id, 'setup.title');
      const description = await client.translate(interaction.guild.id, 'setup.description');
      const helpText = await client.translate(interaction.guild.id, 'setup.help_text');
      const editPanelsButton = await client.translate(interaction.guild.id, 'setup.edit_panels_button');
      const createPanelButton = await client.translate(interaction.guild.id, 'setup.create_panel_button');

      // العودة إلى واجهة الإعداد الأساسية
      const embed = new EmbedBuilder()
        .setColor(client.config.embedColor)
        .setTitle(title)
        .setDescription(description)
        .addFields(
          { name: '\u200B', value: helpText }
        )
        .setImage('https://media.discordapp.net/attachments/1117593323167830107/1370364076915687484/Ticket_AR_1.png?ex=681f3a80&is=681de900&hm=3e983b2d29c39c6354b4026eeaf973d0c7b86001cc7bf650e63268abbe3bed35&=&format=webp&quality=lossless&width=1529&height=813'); // استبدل هذا برابط صورة مناسبة

      const row = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('edit_panels')
            .setLabel(editPanelsButton)
            .setStyle(ButtonStyle.Primary)
            .setEmoji('📋'),
          new ButtonBuilder()
            .setCustomId('create_panel')
            .setLabel(createPanelButton)
            .setStyle(ButtonStyle.Success)
            .setEmoji('➕')
        );

      await interaction.update({ embeds: [embed], components: [row] });
    }
  }
};

// دالة لعرض الخطوة 1: تعيين اسم اللوحة
async function showStep1(interaction, client, state) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'setup.steps.step1.title');
  const description = await client.translate(interaction.guild.id, 'setup.steps.step1.description');
  const currentName = await client.translate(interaction.guild.id, 'setup.steps.step1.current_name');
  const setNameButton = await client.translate(interaction.guild.id, 'setup.steps.step1.set_name_button');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  embed.addFields({ name: currentName, value: state.panelName || 'New Panel' });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('set_panel_name')
        .setLabel(setNameButton)
        .setStyle(ButtonStyle.Primary)
        .setEmoji('🔄')
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('setup_continue')
        .setLabel(continueButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لعرض الخطوة 2: اختيار أدوار فريق الدعم
async function showStep2(interaction, client, state) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'setup.steps.step2.title');
  const description = await client.translate(interaction.guild.id, 'setup.steps.step2.description');
  const selectedRolesLabel = await client.translate(interaction.guild.id, 'setup.steps.step2.selected_roles');
  const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step2.none_selected');
  const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step2.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // الحصول على جميع الأدوار في السيرفر
  const roles = interaction.guild.roles.cache
    .filter(role => !role.managed && role.id !== interaction.guild.id)
    .map(role => ({
      label: role.name,
      value: role.id,
      description: `Role ID: ${role.id}`
    }));

  const selectedRolesText = state.supportRoles.length > 0
    ? state.supportRoles.map(roleId => `<@&${roleId}>`).join(', ')
    : noneSelected;

  embed.addFields({ name: selectedRolesLabel, value: selectedRolesText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_support_roles')
        .setPlaceholder(selectPlaceholder)
        .setMinValues(0)
        .setMaxValues(roles.length > 25 ? 25 : roles.length)
        .addOptions(roles.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('setup_continue')
        .setLabel(continueButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لعرض الخطوة 3: اختيار فئة التذاكر
async function showStep3(interaction, client, state) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'setup.steps.step3.title');
  const description = await client.translate(interaction.guild.id, 'setup.steps.step3.description');
  const selectedCategoriesLabel = await client.translate(interaction.guild.id, 'setup.steps.step3.selected_categories');
  const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step3.none_selected');
  const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step3.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // الحصول على جميع فئات القنوات في السيرفر
  const categories = interaction.guild.channels.cache
    .filter(channel => channel.type === 4) // CategoryChannel
    .map(category => ({
      label: category.name,
      value: category.id,
      description: `Category ID: ${category.id}`
    }));

  const selectedCategoryText = state.ticketCategory
    ? `<#${state.ticketCategory}>`
    : noneSelected;

  embed.addFields({ name: selectedCategoriesLabel, value: selectedCategoryText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_ticket_category')
        .setPlaceholder(selectPlaceholder)
        .addOptions(categories.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('setup_continue')
        .setLabel(continueButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لعرض الخطوة 4: اختيار قناة النسخ
async function showStep4(interaction, client, state) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'setup.steps.step4.title');
  const description = await client.translate(interaction.guild.id, 'setup.steps.step4.description');
  const selectedChannelLabel = await client.translate(interaction.guild.id, 'setup.steps.step4.selected_channel');
  const notSelected = await client.translate(interaction.guild.id, 'setup.steps.step4.not_selected');
  const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step4.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const continueButton = await client.translate(interaction.guild.id, 'setup.continue_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // الحصول على جميع القنوات النصية في السيرفر
  const textChannels = interaction.guild.channels.cache
    .filter(channel => channel.type === 0) // TextChannel
    .map(channel => ({
      label: channel.name,
      value: channel.id,
      description: `Channel ID: ${channel.id}`
    }));

  const selectedChannelText = state.transcriptChannel
    ? `<#${state.transcriptChannel}>`
    : notSelected;

  embed.addFields({ name: selectedChannelLabel, value: selectedChannelText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_transcript_channel')
        .setPlaceholder(selectPlaceholder)
        .addOptions(textChannels.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('setup_continue')
        .setLabel(continueButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}
