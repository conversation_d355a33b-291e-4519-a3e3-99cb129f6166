const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'select_transcript_channel',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      if (!interaction.replied && !interaction.deferred) {
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }
    }

    // تحديث قناة النسخ المحددة
    state.transcriptChannel = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const selected<PERSON>hannelLabel = await client.translate(interaction.guild.id, 'setup.steps.step4.selected_channel');
    const notSelected = await client.translate(interaction.guild.id, 'setup.steps.step4.not_selected');

    // إعادة عرض الخطوة الحالية مع القيم المحدثة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    // الحصول على جميع القنوات النصية في السيرفر
    const textChannels = interaction.guild.channels.cache
      .filter(channel => channel.type === 0) // TextChannel
      .map(channel => ({
        label: channel.name,
        value: channel.id,
        description: `Channel ID: ${channel.id}`
      }));

    const selectedChannelText = state.transcriptChannel
      ? `<#${state.transcriptChannel}>`
      : notSelected;

    embed.addFields({ name: selectedChannelLabel, value: selectedChannelText });

    // الحصول على ترجمات إضافية
    const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step4.select_placeholder');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_transcript_channel')
          .setPlaceholder(selectPlaceholder)
          .addOptions(textChannels.slice(0, 25))
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
