const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton<PERSON><PERSON>le, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'select_ticket_category',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // تحديث فئة التذاكر المحددة
    state.ticketCategory = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const selectedCategoriesLabel = await client.translate(interaction.guild.id, 'setup.steps.step3.selected_categories');
    const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step3.none_selected');

    // إعادة عرض الخطوة الحالية مع القيم المحدثة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    // الحصول على جميع فئات القنوات في السيرفر
    const categories = interaction.guild.channels.cache
      .filter(channel => channel.type === 4) // CategoryChannel
      .map(category => ({
        label: category.name,
        value: category.id,
        description: `Category ID: ${category.id}`
      }));

    const selectedCategoryText = state.ticketCategory
      ? `<#${state.ticketCategory}>`
      : noneSelected;

    embed.addFields({ name: selectedCategoriesLabel, value: selectedCategoryText });

    // الحصول على ترجمات إضافية
    const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step3.select_placeholder');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_ticket_category')
          .setPlaceholder(selectPlaceholder)
          .addOptions(categories.slice(0, 25))
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
