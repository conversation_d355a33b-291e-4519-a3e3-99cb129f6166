const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_category_action',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // تحديث الإجراء المحدد
    state.selectedCategoryAction = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // معالجة الإجراء المحدد
    switch (state.selectedCategoryAction) {
      case 'add_new_category':
        await handleAddNewCategory(interaction, client, state, selectedPanel);
        break;
      case 'edit_category':
        await handleEditCategory(interaction, client, state, selectedPanel);
        break;
      case 'delete_category':
        await handleDeleteCategory(interaction, client, state, selectedPanel);
        break;
      default:
        // إعادة عرض قائمة الإجراءات
        await showCategoryActionMenu(interaction, client, state, selectedPanel);
        break;
    }
  }
};

// دالة لعرض قائمة إجراءات الفئات
async function showCategoryActionMenu(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.category_actions.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.category_actions.description');
  const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.category_actions.placeholder');
  const addNewLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.add_new');
  const editLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.edit');
  const deleteLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.delete');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // إضافة معلومات عن الفئات الحالية
  if (selectedPanel.categories && selectedPanel.categories.length > 0) {
    const categoriesInfo = selectedPanel.categories.map((cat, index) =>
      `${index + 1}. ${cat.name}`
    ).join('\n');

    embed.addFields({ name: 'الفئات الحالية', value: categoriesInfo });
  } else {
    embed.addFields({ name: 'الفئات الحالية', value: 'لا توجد فئات حالية' });
  }

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_category_action')
        .setPlaceholder(actionPlaceholder)
        .addOptions([
          {
            label: addNewLabel,
            value: 'add_new_category',
            description: 'إضافة فئة/زر جديد',
            emoji: '1️⃣'
          },
          {
            label: editLabel,
            value: 'edit_category',
            description: 'تعديل فئة/زر حالي',
            emoji: '2️⃣'
          },
          {
            label: deleteLabel,
            value: 'delete_category',
            description: 'حذف فئة/زر حالي',
            emoji: '3️⃣'
          }
        ])
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️')
    );

  try {
    // تأجيل الرد إذا لم يتم الرد بعد
    if (!interaction.deferred && !interaction.replied) {
      await interaction.deferUpdate().catch(e => console.error('Error deferring update:', e));
    }

    // محاولة تحديث التفاعل
    await interaction.editReply({ embeds: [embed], components: [row1, row2] }).catch(async (error) => {
      console.error('Error updating interaction:', error);

      // محاولة إرسال رسالة متابعة إذا فشل التحديث
      await interaction.followUp({
        embeds: [embed],
        components: [row1, row2],
        ephemeral: true
      }).catch(followUpError => {
        console.error('Failed to follow up on interaction:', followUpError);
      });
    });
  } catch (error) {
    console.error('Unexpected error in interaction handling:', error);
  }
}

// دالة لمعالجة إضافة فئة/زر جديد
async function handleAddNewCategory(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const modalTitle = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.title');
  const nameLabel = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.name_label');
  const namePlaceholder = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.name_placeholder');
  const descriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.description_label');
  const descriptionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.description_placeholder');
  const emojiLabel = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.emoji_label');
  const emojiPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.add_category_modal.emoji_placeholder');

  const modal = new ModalBuilder()
    .setCustomId('add_category_modal')
    .setTitle(modalTitle);

  const nameInput = new TextInputBuilder()
    .setCustomId('category_name_input')
    .setLabel(nameLabel)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(namePlaceholder)
    .setRequired(true);

  const descriptionInput = new TextInputBuilder()
    .setCustomId('category_description_input')
    .setLabel(descriptionLabel)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(descriptionPlaceholder)
    .setRequired(true);

  const emojiInput = new TextInputBuilder()
    .setCustomId('category_emoji_input')
    .setLabel(emojiLabel)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(emojiPlaceholder)
    .setRequired(false);

  const nameRow = new ActionRowBuilder().addComponents(nameInput);
  const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);
  const emojiRow = new ActionRowBuilder().addComponents(emojiInput);

  modal.addComponents(nameRow, descriptionRow, emojiRow);

  await interaction.showModal(modal);
}

// دالة لمعالجة تعديل فئة/زر حالي
async function handleEditCategory(interaction, client, state, selectedPanel) {
  // التحقق من وجود فئات
  if (!selectedPanel.categories || selectedPanel.categories.length === 0) {
    const errorMessage = await client.translate(interaction.guild.id, 'errors.no_categories');
    return interaction.followUp({
      content: errorMessage,
      ephemeral: true
    });
  }

  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.edit_category.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.edit_category.description');
  const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_category.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // إنشاء خيارات للفئات الحالية
  const categoryOptions = selectedPanel.categories.map((category, index) => ({
    label: category.name,
    value: `${index}`,
    description: category.description.substring(0, 50) + (category.description.length > 50 ? '...' : '')
  }));

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_category_to_edit')
        .setPlaceholder(selectPlaceholder)
        .addOptions(categoryOptions)
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️')
    );

  try {
    // تأجيل الرد إذا لم يتم الرد بعد
    if (!interaction.deferred && !interaction.replied) {
      await interaction.deferUpdate().catch(e => console.error('Error deferring update:', e));
    }

    // محاولة تحديث التفاعل
    await interaction.editReply({ embeds: [embed], components: [row1, row2] }).catch(async (error) => {
      console.error('Error updating interaction:', error);

      // محاولة إرسال رسالة متابعة إذا فشل التحديث
      await interaction.followUp({
        embeds: [embed],
        components: [row1, row2],
        ephemeral: true
      }).catch(followUpError => {
        console.error('Failed to follow up on interaction:', followUpError);
      });
    });
  } catch (error) {
    console.error('Unexpected error in interaction handling:', error);
  }
}

// دالة لمعالجة حذف فئة/زر حالي
async function handleDeleteCategory(interaction, client, state, selectedPanel) {
  // التحقق من وجود فئات
  if (!selectedPanel.categories || selectedPanel.categories.length === 0) {
    const errorMessage = await client.translate(interaction.guild.id, 'errors.no_categories');
    return interaction.followUp({
      content: errorMessage,
      ephemeral: true
    });
  }

  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.delete_category.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.delete_category.description');
  const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.delete_category.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // إنشاء خيارات للفئات الحالية
  const categoryOptions = selectedPanel.categories.map((category, index) => ({
    label: category.name,
    value: `${index}`,
    description: category.description.substring(0, 50) + (category.description.length > 50 ? '...' : '')
  }));

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_category_to_delete')
        .setPlaceholder(selectPlaceholder)
        .addOptions(categoryOptions)
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️')
    );

  try {
    // تأجيل الرد إذا لم يتم الرد بعد
    if (!interaction.deferred && !interaction.replied) {
      await interaction.deferUpdate().catch(e => console.error('Error deferring update:', e));
    }

    // محاولة تحديث التفاعل
    await interaction.editReply({ embeds: [embed], components: [row1, row2] }).catch(async (error) => {
      console.error('Error updating interaction:', error);

      // محاولة إرسال رسالة متابعة إذا فشل التحديث
      await interaction.followUp({
        embeds: [embed],
        components: [row1, row2],
        ephemeral: true
      }).catch(followUpError => {
        console.error('Failed to follow up on interaction:', followUpError);
      });
    });
  } catch (error) {
    console.error('Unexpected error in interaction handling:', error);
  }
}
