const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'setup_finish',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // التحقق من اكتمال جميع البيانات المطلوبة
    if (!state.targetChannel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.select_channel');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // إنشاء لوحة التذاكر
    try {
      const targetChannel = interaction.guild.channels.cache.get(state.targetChannel);

      if (!targetChannel) {
        const errorMessage = await client.translate(interaction.guild.id, 'errors.channel_not_found');
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }

      // الحصول على الترجمات
      const ticketDescription = await client.translate(interaction.guild.id, 'ticket.create_button');

      // إنشاء مكونات اللوحة حسب نوع العرض
      let row;

      // نوع العرض الافتراضي هو dropdown_message
      const displayType = state.displayType || 'dropdown_message';

      // الحصول على النصوص الافتراضية من ملفات الترجمة
      const defaultDescription = await client.translate(interaction.guild.id, 'panel_editor.default_description');
      const defaultWelcomeMessage = await client.translate(interaction.guild.id, 'panel_editor.default_welcome_message');

      // إنشاء لوحة التذاكر في قاعدة البيانات أولاً للحصول على معرف اللوحة
      const panelData = {
        name: state.panelName,
        description: state.description || defaultDescription,
        welcomeMessage: state.welcomeMessage || defaultWelcomeMessage,
        displayType: state.displayType || 'dropdown_message',
        categories: state.categories || [],
        supportRoles: state.supportRoles,
        ticketCategory: state.ticketCategory,
        transcriptChannel: state.transcriptChannel,
        imageUrl: state.imageUrl || ''
      };

      const panel = await panelService.createPanel(interaction.guild.id, panelData, interaction.user.id);

      if (!panel) {
        console.error('فشل حفظ اللوحة في قاعدة البيانات');
        const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_creation_error');
        return interaction.reply({
          content: errorMessage,
          ephemeral: true
        });
      }

      console.log(`تم حفظ اللوحة في قاعدة البيانات بمعرف: ${panel.panelId}`);

      if (displayType.startsWith('dropdown')) {
        // إنشاء قائمة منسدلة
        const options = state.categories && state.categories.length > 0
          ? state.categories.map(category => ({
              label: category.name,
              description: category.description,
              value: `ticket_category_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`,
              emoji: category.emoji
            }))
          : [{ label: ticketDescription, description: 'إنشاء تذكرة دعم جديدة', value: `create_ticket_${panel.panelId}`, emoji: '🎫' }];

        row = new ActionRowBuilder()
          .addComponents(
            new StringSelectMenuBuilder()
              .setCustomId(`create_ticket_${panel.panelId}`)
              .setPlaceholder(ticketDescription)
              .addOptions(options)
          );
      } else {
        // إنشاء أزرار
        const buttons = state.categories && state.categories.length > 0
          ? state.categories.map(category =>
              new ButtonBuilder()
                .setCustomId(`create_ticket_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`)
                .setLabel(category.name)
                .setStyle(ButtonStyle.Primary)
                .setEmoji(category.emoji)
            )
          : [
              new ButtonBuilder()
                .setCustomId(`create_ticket_${panel.panelId}`)
                .setLabel(ticketDescription)
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🎫')
            ];

        row = new ActionRowBuilder().addComponents(buttons);
      }

      // إرسال الرسالة حسب نوع العرض
      let sentMessage;

      if ((displayType === 'dropdown_image' || displayType === 'buttons_image') && state.imageUrl) {
        // إرسال صورة مباشرة بدون إمبد
        sentMessage = await targetChannel.send({
          files: [state.imageUrl],
          components: [row]
        });
      } else {
        // إرسال إمبد مع وصف
        const panelEmbed = new EmbedBuilder()
          .setColor(client.config.embedColor)
          .setTitle(`${state.panelName}`)
          .setDescription(state.description || defaultDescription)
          .setFooter({ text: client.config.footerText });

        sentMessage = await targetChannel.send({
          embeds: [panelEmbed],
          components: [row]
        });
      }

      // تحديث معرف الرسالة والقناة
      await panelService.updatePanelMessage(panel.panelId, sentMessage.id, targetChannel.id);

      // الحصول على الترجمات
      const title = await client.translate(interaction.guild.id, 'setup.title');
      const description = await client.translate(interaction.guild.id, 'setup.description');
      const helpText = await client.translate(interaction.guild.id, 'setup.help_text');
      const editPanelsButton = await client.translate(interaction.guild.id, 'setup.edit_panels_button');
      const createPanelButton = await client.translate(interaction.guild.id, 'setup.create_panel_button');
      const panelCreatedMessage = await client.translate(interaction.guild.id, 'setup.panel_created', { channel: targetChannel.toString() });

      // إنشاء واجهة الإعداد الأساسية للعودة إليها
      const setupEmbed = new EmbedBuilder()
        .setColor(client.config.embedColor)
        .setTitle(title)
        .setDescription(description)
        .addFields(
          { name: '\u200B', value: helpText }
        )
        .setImage('https://media.discordapp.net/attachments/1117593323167830107/1370364076915687484/Ticket_AR_1.png?ex=681f3a80&is=681de900&hm=3e983b2d29c39c6354b4026eeaf973d0c7b86001cc7bf650e63268abbe3bed35&=&format=webp&quality=lossless&width=1529&height=813'); // استبدل هذا برابط صورة مناسبة

      const setupRow = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('edit_panels')
            .setLabel(editPanelsButton)
            .setStyle(ButtonStyle.Primary)
            .setEmoji('📋'),
          new ButtonBuilder()
            .setCustomId('create_panel')
            .setLabel(createPanelButton)
            .setStyle(ButtonStyle.Success)
            .setEmoji('➕')
        );

      // تحديث الرسالة للعودة إلى الواجهة الرئيسية
      await interaction.update({
        embeds: [setupEmbed],
        components: [setupRow]
      });

      // إرسال رسالة تأكيد منفصلة
      await interaction.followUp({
        content: panelCreatedMessage,
        ephemeral: true
      });

      // مسح حالة الإعداد
      setupStates.delete(interaction.user.id);
    } catch (error) {
      console.error('Error creating ticket panel:', error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_creation_error');
      await interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }
  }
};
