const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, EmbedBuilder, PermissionsBitField } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('debug')
    .setDescription('Debug bot issues in a specific channel'),

  async execute(interaction, client) {
    // التحقق من صلاحيات المستخدم
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.ManageChannels)) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.permission_denied');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // التحقق من صلاحيات البوت في القناة الحالية
    const channel = interaction.channel;
    const botMember = interaction.guild.members.cache.get(client.user.id);

    const requiredPermissions = [
      PermissionsBitField.Flags.ViewChannel,
      PermissionsBitField.Flags.SendMessages,
      PermissionsBitField.Flags.EmbedLinks,
      PermissionsBitField.Flags.AttachFiles,
      PermissionsBitField.Flags.ReadMessageHistory,
      PermissionsBitField.Flags.ManageChannels
    ];

    const missingPermissions = [];

    for (const permission of requiredPermissions) {
      if (!botMember.permissionsIn(channel).has(permission)) {
        missingPermissions.push(permission);
      }
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'debug.title');
    const description = await client.translate(interaction.guild.id, 'debug.description', { channel: channel.toString() });
    const connectionStatus = await client.translate(interaction.guild.id, 'debug.connection_status');
    const botPermissions = await client.translate(interaction.guild.id, 'debug.bot_permissions');
    const permissionsComplete = await client.translate(interaction.guild.id, 'debug.permissions_complete');
    const permissionsMissing = await client.translate(interaction.guild.id, 'debug.permissions_missing');
    const missingPermissionsText = await client.translate(interaction.guild.id, 'debug.missing_permissions');

    const embed = new EmbedBuilder()
      .setColor(missingPermissions.length > 0 ? '#ff0000' : '#00ff00')
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: connectionStatus, value: client.ws.ping + 'ms' },
        { name: botPermissions, value: missingPermissions.length > 0 ? permissionsMissing : permissionsComplete }
      )
      .setFooter({ text: client.config.footerText });

    if (missingPermissions.length > 0) {
      const permissionNames = missingPermissions.map(p => {
        const permName = Object.keys(PermissionsBitField.Flags).find(key => PermissionsBitField.Flags[key] === p);
        return permName || 'Unknown Permission';
      });

      embed.addFields({ name: missingPermissionsText, value: permissionNames.join('\n') });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }
};
