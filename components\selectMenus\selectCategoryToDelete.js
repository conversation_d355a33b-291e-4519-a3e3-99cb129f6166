const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_category_to_delete',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // تحديث الفئة المحددة
    state.selectedCategoryIndex = parseInt(interaction.values[0]);
    setupStates.set(interaction.user.id, state);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على الفئة المحددة
    const selectedCategory = selectedPanel.categories[state.selectedCategoryIndex];

    if (!selectedCategory) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.category_not_found');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.delete_category_confirmation.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.delete_category_confirmation.description', { category_name: selectedCategory.name });
    const cancelButton = await client.translate(interaction.guild.id, 'panel_editor.delete_category_confirmation.cancel_button');
    const deleteButton = await client.translate(interaction.guild.id, 'panel_editor.delete_category_confirmation.delete_button');

    const embed = new EmbedBuilder()
      .setColor('#ff0000')
      .setTitle(title)
      .setDescription(description);

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(cancelButton)
          .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
          .setCustomId('confirm_delete_category')
          .setLabel(deleteButton)
          .setStyle(ButtonStyle.Danger)
      );

    await interaction.update({ embeds: [embed], components: [row] });
  }
};
