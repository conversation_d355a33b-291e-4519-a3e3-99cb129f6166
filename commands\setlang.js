const { SlashCommandBuilder, PermissionsBitField } = require('discord.js');
const { setGuildLanguage, getSupportedLanguages, translate } = require('../utils/localeManager');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setlang')
    .setDescription('Change the bot language')
    .addStringOption(option =>
      option.setName('language')
        .setDescription('Choose the language (ar for Arabic, en for English)')
        .setRequired(true)
        .addChoices(
          { name: 'العربية (Arabic)', value: 'ar' },
          { name: 'English', value: 'en' }
        )
    ),
  
  async execute(interaction, client) {
    // التحقق من صلاحيات المستخدم
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.ManageGuild)) {
      const errorMessage = await translate(interaction.guild.id, 'errors.permission_denied');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }
    
    const language = interaction.options.getString('language');
    const supportedLanguages = getSupportedLanguages();
    
    if (!supportedLanguages.includes(language)) {
      const invalidLanguageMessage = await translate(interaction.guild.id, 'setlang.invalid_language');
      return interaction.reply({
        content: invalidLanguageMessage,
        ephemeral: true
      });
    }
    
    const success = await setGuildLanguage(interaction.guild.id, language);
    
    if (success) {
      // استخدام اللغة الجديدة للرد
      const successMessage = await translate(interaction.guild.id, 'setlang.success', { language: language === 'ar' ? 'العربية' : 'English' });
      await interaction.reply({
        content: successMessage,
        ephemeral: true
      });
    } else {
      const errorMessage = await translate(interaction.guild.id, 'errors.command_error');
      await interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }
  }
};
