const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON>tonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'confirm_delete_panel',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // حذف اللوحة من قاعدة البيانات
    const deleted = await panelService.deletePanel(state.selectedPanel);

    if (!deleted) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.delete_error');
      return interaction.reply({
        content: errorMessage,
        ephemeral: true
      });
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');
    const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.select_placeholder');
    const noPanelsLabel = await client.translate(interaction.guild.id, 'panel_editor.no_panels.label');
    const noPanelsDescription = await client.translate(interaction.guild.id, 'panel_editor.no_panels.description');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');
    const panelDeletedMessage = await client.translate(interaction.guild.id, 'panel_editor.panel_deleted', { panel_name: selectedPanel.name });

    // إعادة عرض واجهة محرر اللوحات
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '\u200B', value: unsavedChanges }
      );

    // الحصول على قائمة اللوحات المحدثة من قاعدة البيانات
    const updatedPanels = await panelService.getPanelsByGuild(interaction.guild.id);

    const panelOptions = updatedPanels.length > 0
      ? updatedPanels.map(panel => ({
          label: panel.name,
          value: panel.panelId,
          description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
        }))
      : [];

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_panel_to_edit')
          .setPlaceholder(selectPlaceholder)
          .addOptions(panelOptions.length > 0 ? panelOptions : [
            {
              label: noPanelsLabel,
              value: 'no_panels',
              description: noPanelsDescription
            }
          ])
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });

    // إرسال رسالة تأكيد
    await interaction.followUp({
      content: panelDeletedMessage,
      ephemeral: true
    });

    // إعادة تعيين حالة المستخدم
    state.selectedPanel = null;
    state.selectedAction = null;
    setupStates.set(interaction.user.id, state);
  }
};
